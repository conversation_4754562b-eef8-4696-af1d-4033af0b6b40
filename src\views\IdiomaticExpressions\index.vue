<!--惯用语设置-->
<template>
  <div class="phrase-settings" :class="elderModeClass">
    <!-- 导航栏 -->
    <van-nav-bar
      title="惯用语设置"
      left-arrow
      fixed
      @click-left="onClickLeft"
    />

    <!-- 下拉刷新 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <!-- 空状态 -->
      <div class="empty-state" v-if="phraseList.length === 0 && !isLoading">
        <img src="@/assets/images/empty-bird.png" class="empty-image" />
        <van-button plain type="info" @click="showAddPopup">
          <van-icon name="plus" /> 新增惯用语
        </van-button>
      </div>

      <!-- 惯用语列表 -->
      <div class="phrase-list" v-else>
        <div
          class="phrase-item"
          v-for="(item, index) in phraseList"
          :key="index"
        >
          <div class="phrase-header">
            <div class="phrase-index">{{ index + 1 }}.</div>
            <div class="phrase-content">{{ item.phrase }}</div>
          </div>
          <div class="phrase-actions">
            <div class="action-item" @click="deletePhrase(index)">
              <van-icon name="delete-o" class="action-icon" color="#428FFC"/>
              <span class="action-text">删除</span>
            </div>
            <div class="action-item" @click="editPhrase(index)">
              <van-icon name="edit" class="action-icon" color="#428FFC"/>
              <span class="action-text">编辑</span>
            </div>
            <div class="action-item" @click="viewPhrase(index)">
              <van-icon name="eye-o" class="action-icon" color="#428FFC"/>
              <span class="action-text">查看</span>
            </div>
          </div>
        </div>
      </div>
    </van-pull-refresh>

    <!-- 底部新增按钮 -->
    <div class="bottom-btn" v-if="phraseList.length > 0">
      <van-button type="info" block @click="showAddPopup" style="border-radius: 5px">新增</van-button>
    </div>

    <!-- 新增/编辑弹窗 -->
    <van-popup v-model="showPopup" round class="popup">
      <div class="popup-title">{{ isEdit ? '编辑' : '新增' }}</div>
      <div class="popup-content">
        <van-field
          v-model="currentPhrase"
          type="textarea"
          placeholder="请输入"
          rows="4"
          autosize
        />
      </div>
      <div class="popup-buttons">
        <van-button plain class="cancel-btn" @click="cancelEdit">取消</van-button>
        <van-button type="info" plain class="confirm-btn" @click="confirmEdit" :loading="submitLoading">确定</van-button>
      </div>
    </van-popup>

    <!-- 查看弹窗 -->
    <van-popup v-model="showViewPopup" round class="popup">
      <div class="popup-title">查看惯用语</div>
      <div class="popup-content">
        <div class="view-content">{{ currentPhrase }}</div>
      </div>
      <div class="popup-buttons">
        <van-button type="info" plain block class="confirm-btn" @click="showViewPopup = false">关闭</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getPhraseList, addPhrase, updatePhrase, deletePhrase } from '@/api/common'
import elderMode from "@/mixins/elderMode";

export default {
  name: 'PhraseSettings',
  mixins: [elderMode],
  data() {
    return {
      phraseList: [],
      showPopup: false,
      showViewPopup: false,
      currentPhrase: '',
      currentIndex: -1,
      isEdit: false,
      isLoading: false,
      submitLoading: false,
      refreshing: false,
      // 分页参数
      queryParams: {
        pageNum: 1,
        pageSize: 20
      }
    }
  },
  mounted() {
    // 初始加载数据
    this.getList();
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },

    // 获取惯用语列表
    async getList() {
      try {
        this.isLoading = true;

        // 显示加载提示
        this.$toast.loading({
          message: '加载中...',
          forbidClick: true,
          duration: 0
        });

        // 调用实际API
        const res = await getPhraseList(this.queryParams);

        if (res.code === 200) {
          this.phraseList = res.rows || [];
        } else {
          this.$toast.fail(res.msg || '获取列表失败');
        }

        // 关闭加载提示
        this.$toast.clear();
      } catch (error) {
        console.error('获取惯用语列表失败:', error);
        this.$toast.fail('获取列表失败');
      } finally {
        this.isLoading = false;
        // 如果是刷新操作，结束刷新状态
        if (this.refreshing) {
          this.refreshing = false;
        }
      }
    },

    // 下拉刷新
    async onRefresh() {
      await this.getList();
    },

    // 显示新增弹窗
    showAddPopup() {
      this.isEdit = false;
      this.currentPhrase = '';
      this.currentIndex = -1;
      this.showPopup = true;
    },

    // 编辑惯用语
    editPhrase(index) {
      this.isEdit = true;
      this.currentIndex = index;
      this.currentPhrase = this.phraseList[index].phrase;
      this.showPopup = true;
    },

    // 查看惯用语
    viewPhrase(index) {
      this.currentPhrase = this.phraseList[index].phrase;
      this.showViewPopup = true;
    },

    // 删除惯用语
    deletePhrase(index) {
      this.$dialog.confirm({
        title: '提示',
        message: '确定要删除这条惯用语吗？',
      }).then(async () => {
        try {
          const phraseId = this.phraseList[index].id;

          // 显示加载提示
          this.$toast.loading({
            message: '删除中...',
            forbidClick: true,
            duration: 0
          });

          // 调用删除接口
          const res = await deletePhrase(phraseId);

          if (res.code === 200) {
            this.$toast.success('删除成功');
            // 刷新列表
            this.getList();
          } else {
            this.$toast.fail(res.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除惯用语失败:', error);
          this.$toast.fail('删除失败');
        }
      }).catch(() => {
        // 取消删除
      });
    },

    // 取消编辑
    cancelEdit() {
      this.showPopup = false;
      this.currentPhrase = '';
    },

    // 确认编辑/新增
    async confirmEdit() {
      if (!this.currentPhrase.trim()) {
        this.$toast('请输入惯用语内容');
        return;
      }

      try {
        this.submitLoading = true;

        if (this.isEdit) {
          // 编辑现有惯用语
          const phraseData = {
            id: this.phraseList[this.currentIndex].id,
            phrase: this.currentPhrase
          };

          const res = await updatePhrase(phraseData);

          if (res.code === 200) {
            this.$toast.success('编辑成功');
            this.showPopup = false;
            this.currentPhrase = '';
            // 刷新列表
            this.getList();
          } else {
            this.$toast.fail(res.msg || '编辑失败');
          }
        } else {
          // 新增惯用语
          const phraseData = {
            phrase: this.currentPhrase
          };

          const res = await addPhrase(phraseData);

          if (res.code === 200) {
            this.$toast.success('新增成功');
            this.showPopup = false;
            this.currentPhrase = '';
            // 刷新列表
            this.getList();
          } else {
            this.$toast.fail(res.msg || '新增失败');
          }
        }
      } catch (error) {
        console.error(this.isEdit ? '编辑惯用语失败:' : '新增惯用语失败:', error);
        this.$toast.fail(this.isEdit ? '编辑失败' : '新增失败');
      } finally {
        this.submitLoading = false;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.phrase-settings {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-top: 46px;
  padding-bottom: 70px;
}

// 下拉刷新区域
.van-pull-refresh {
  min-height: calc(100vh - 46px - 60px);
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;

  .empty-image {
    width: 200px;
    height: 200px;
    margin-bottom: 30px;
  }
}

// 惯用语列表
.phrase-list {
  padding: 15px;

  .phrase-item {
    margin-bottom: 12px;

    .phrase-header {
      background-color: #fff;
      border-radius: 8px;
      padding: 15px;
      display: flex;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .phrase-index {
        font-weight: bold;
        color: #333;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .phrase-content {
        flex: 1;
        color: #333;
        line-height: 1.5;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
    }

    .phrase-actions {
      display: flex;
      justify-content: flex-end;

      .action-item {
        display: flex;
        align-items: center;
        padding: 0 12px;
        color: #666;
        font-size: 13px;

        .action-text {
          color: #428FFC;
        }

        .action-icon {
          font-size: 16px;
          margin-right: 4px;
        }

        &:active {
          color: #1989fa;
        }
      }
    }
  }
}

// 底部按钮
.bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px 15px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

// 弹窗样式
.popup {
  width: 80%;
  border-radius: 12px;
  overflow: hidden;

  .popup-title {
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
  }

  .popup-content {
    padding: 15px;

    .view-content {
      min-height: 100px;
      line-height: 1.6;
      color: #333;
    }
  }

  .popup-buttons {
    display: flex;
    border-top: 1px solid #eee;

    .van-button {
      flex: 1;
      border: none;
      border-radius: 0;
      height: 44px;
      font-size: 16px;
    }

    .cancel-btn {
      color: #666;
    }
  }
}
</style>
