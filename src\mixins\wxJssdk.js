/**
 * 微信JSSDK配置混入
 * 提供微信JSSDK相关的通用方法和状态管理
 */
import { initWxJssdk, isWechatBrowser, isWxJssdkReady, resetWxJssdkState, getWxJssdkState } from '@/util/wxJssdk'

export default {
  data() {
    return {
      wxJssdkReady: false, // 微信JSSDK是否已配置
      wxConfiguring: false, // 是否正在配置微信JSSDK
      wxInitRetryCount: 0, // 初始化重试次数
      wxMaxRetries: 3, // 最大重试次数
    }
  },

  mounted() {
    // 延迟初始化，确保页面完全加载
    this.$nextTick(() => {
      setTimeout(() => {
        this.autoInitWxJssdk()
      }, 500)
    })
  },

  beforeDestroy() {
    // 组件销毁时清理状态
    this.wxConfiguring = false
  },

  methods: {
    /**
     * 自动初始化微信JSSDK配置
     * @param {Array} jsApiList - 需要的JS接口列表
     */
    async autoInitWxJssdk(jsApiList = ['scanQRCode']) {
      console.log('开始自动初始化微信JSSDK...')

      // 检查是否在微信环境中
      if (!isWechatBrowser()) {
        console.log('不在微信环境中，跳过JSSDK配置')
        return false
      }

      // 检查是否已经配置过
      if (isWxJssdkReady()) {
        this.wxJssdkReady = true
        console.log('微信JSSDK已配置')
        return true
      }

      // 如果正在配置中，避免重复配置
      if (this.wxConfiguring) {
        console.log('微信JSSDK正在配置中，等待完成...')
        return false
      }

      this.wxConfiguring = true
      this.wxInitRetryCount++

      try {
        console.log(`第${this.wxInitRetryCount}次尝试配置微信JSSDK...`)
        await initWxJssdk(jsApiList)
        this.wxJssdkReady = true
        this.wxInitRetryCount = 0 // 重置重试计数
        console.log('微信JSSDK配置成功')

        // 配置成功后的回调
        this.$emit('wx-jssdk-ready')

        return true
      } catch (error) {
        console.error(`第${this.wxInitRetryCount}次微信JSSDK配置失败:`, error)

        // 如果还有重试机会，延迟重试
        if (this.wxInitRetryCount < this.wxMaxRetries) {
          console.log(`将在2秒后进行第${this.wxInitRetryCount + 1}次重试...`)
          setTimeout(() => {
            if (!this.wxJssdkReady) {
              this.autoInitWxJssdk(jsApiList)
            }
          }, 2000)
        } else {
          // 重试次数用完，显示错误信息
          console.error('微信JSSDK配置失败，已达到最大重试次数')
          if (this.$toast) {
            this.$toast.fail('微信功能配置失败: ' + error.message)
          }
          this.$emit('wx-jssdk-error', error)
        }

        return false
      } finally {
        this.wxConfiguring = false
      }
    },

    /**
     * 确保微信JSSDK已配置
     * @param {Array} jsApiList - 需要的JS接口列表
     * @returns {Promise<boolean>}
     */
    async ensureWxJssdkReady(jsApiList = ['scanQRCode']) {
      // 如果正在配置，等待配置完成
      if (this.wxConfiguring) {
        return new Promise((resolve) => {
          const checkReady = () => {
            if (!this.wxConfiguring) {
              resolve(this.wxJssdkReady)
            } else {
              setTimeout(checkReady, 100)
            }
          }
          checkReady()
        })
      }

      // 如果已配置，直接返回
      if (this.wxJssdkReady) {
        return true
      }

      // 尝试配置
      return await this.autoInitWxJssdk(jsApiList)
    },

    /**
     * 检查是否在微信环境中
     * @returns {boolean}
     */
    isInWechat() {
      return isWechatBrowser()
    },

    /**
     * 获取微信JSSDK状态文本
     * @returns {string}
     */
    getWxJssdkStatusText() {
      if (!this.isInWechat()) {
        return '请在微信中打开'
      }
      if (this.wxConfiguring) {
        return '正在配置微信功能...'
      }
      if (!this.wxJssdkReady) {
        return '微信功能未配置'
      }
      return '微信功能已就绪'
    },

    /**
     * 显示微信JSSDK状态提示
     */
    showWxJssdkStatus() {
      const status = this.getWxJssdkStatusText()
      if (this.$toast) {
        if (this.wxJssdkReady) {
          this.$toast.success(status)
        } else {
          this.$toast(status)
        }
      }
    },

    /**
     * 重置微信JSSDK状态并重新初始化
     * @param {Array} jsApiList - 需要的JS接口列表
     */
    async resetAndInitWxJssdk(jsApiList = ['scanQRCode']) {
      console.log('重置并重新初始化微信JSSDK...')

      // 重置状态
      resetWxJssdkState()
      this.wxJssdkReady = false
      this.wxConfiguring = false
      this.wxInitRetryCount = 0

      // 重新初始化
      return await this.autoInitWxJssdk(jsApiList)
    },

    /**
     * 获取微信JSSDK详细状态信息（用于调试）
     */
    getWxJssdkDebugInfo() {
      const state = getWxJssdkState()
      const debugInfo = {
        ...state,
        componentState: {
          wxJssdkReady: this.wxJssdkReady,
          wxConfiguring: this.wxConfiguring,
          wxInitRetryCount: this.wxInitRetryCount
        },
        environment: {
          isWechat: isWechatBrowser(),
          url: window.location.href,
          userAgent: navigator.userAgent
        }
      }

      console.log('微信JSSDK调试信息:', debugInfo)
      return debugInfo
    }
  }
}
