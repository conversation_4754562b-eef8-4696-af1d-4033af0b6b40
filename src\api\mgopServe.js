import { mgop } from "@aligov/jssdk-mgop";//需要用npm安装依赖
import { getToken } from "@/util/auth";
export const mgopRequest = async (payload) => {
  try {
    // 正确等待token获取
    let token = ""

    try {
      const tokenResult = await getToken()
      console.log(tokenResult, "-----------token获取结果-----------")

      if (tokenResult && tokenResult['YGF-MOBILE-Token']) {
        token = tokenResult['YGF-MOBILE-Token']
      } else if (typeof tokenResult === 'string' && tokenResult) {
        token = tokenResult
      }

      console.log("最终使用的token:", token)
    } catch (tokenError) {
      console.warn("获取token失败:", tokenError)
      // 在浙里办环境中，可能不需要token或使用其他认证方式
    }

    return new Promise((resolve, reject) => {
      const mgopParams = {
        api: "mgop.xzzfj.ygf.invoke", // 浙里办接口
        host: "https://mapi.zjzwfw.gov.cn/", // 固定host
        dataType: "JSON",
        type: payload.method || "GET",
        appKey: "69aga5zy+2002471897+buwewo", // 项目的appKey
        data: payload.data || {},
        header: token ? { Authorization: token } : {}, // 只有token存在时才设置
        onSuccess: (data) => {
          console.log("mgopRequest成功响应:", data)
          resolve(data.data || data)
        },
        onFail: (err) => {
          console.error("mgopRequest失败:", err)
          reject(err.message || err)
        },
      }

      console.log("mgop请求参数:", mgopParams)
      mgop(mgopParams)
    })
  } catch (error) {
    console.error("mgopRequest异常:", error)
    return Promise.reject(error)
  }
};
