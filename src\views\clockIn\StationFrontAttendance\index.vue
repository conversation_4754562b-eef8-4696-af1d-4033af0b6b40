<template>
	<div class="station-clock-in">

    <!-- 导航栏 -->
    <van-nav-bar
        title="考勤打卡"
        left-arrow
        fixed
        @click-left="$router.go(-1)"
    />

		<!-- 顶部用户信息 -->
		<div class="user-info">
			<div class="avatar">
				<span>{{ nickName.length > 2 ? nickName.slice(-2) : nickName }}</span>
			</div>
			<div class="user-detail">
				<div class="name">{{ nickName }}</div>
				<div class="dept">{{ deptName }}</div>
			</div>
		</div>

    <div class="clock-main">
      <!-- 打卡状态 -->
      <div class="clock-status">
        <div class="clock-item">
          <div class="clock-title">
            上班{{ startTime }}
            <van-icon v-if="punchInInfo.startInTime" name="checked" color="#1989fa" style="position: relative; top: 2.3px"/>
          </div>
          <div class="clock-time">
            <span v-if="punchInInfo.startInTime">{{ punchInInfo.startInTime }}</span>
            <span v-else>未打卡</span>
            <span v-if="punchInInfo.startStatus" class="status-tag"
              :class="{ 'wrong': punchInInfo.startStatus != 1 }">{{ getPunchName(punchInInfo.startStatus) }}</span>
          </div>
        </div>
        <div class="clock-item">
          <div class="clock-title">
            下班{{ endTime }}
            <van-icon v-if="punchInInfo.endOutTime" name="checked" color="#1989fa" style="position: relative; top: 2.3px"/>
          </div>
          <div class="clock-time">
            <span v-if="punchInInfo.endOutTime">{{ punchInInfo.endOutTime }}</span>
            <span v-else>未打卡</span>
            <span v-if="punchInInfo.endStatus" class="status-tag"
              :class="{ 'wrong': punchInInfo.endStatus != 1 }">{{ getPunchName(punchInInfo.endStatus) }}</span>
            <span v-if="punchInInfo.endOutTime" class="update-btn" @click="handleUpdateClock(1)">更新打卡</span>
          </div>
        </div>
      </div>

      <!-- 打卡按钮 -->
      <div class="clock-btn-container">
        <div v-if="showWork" class="clock-btn" @click="handleClock(0)">
          <span class="btn-text">上班打卡</span>
          <span class="btn-date">{{ nTime }}</span>
        </div>
        <div v-else class="clock-btn" @click="handleClock(1)">
          <span class="btn-text">下班打卡</span>
          <span class="btn-date">{{ nTime }}</span>
        </div>

        <!-- 位置信息 -->
        <div class="location-status">
          <div class="status-item location-area">
            <van-icon v-if="isIn" name="success" color="#07c160" size="16" />
            <van-icon v-else name="cross" color="#ee0a24" size="16" />
            <span>{{ isIn ? '当前已在考勤区域内' : '当前未在考勤区域内' }}</span>
          </div>

          <div class="status-item address-item">
            <van-icon name="location-o" color="#999" size="16" />
            <span class="address-text">{{ addressText || '获取位置中...' }}</span>
            <span class="refresh-btn" @click="reLocation">刷新</span>
          </div>
        </div>
      </div>
    </div>

	</div>
</template>

<script>
import { mapState } from 'vuex';
import { timeFormat } from '@/util';
import { getOfficeTodayInfo,getGridTodayInfo, editPunchIn, addPunchIn } from '@/api/clockIn';

export default {
	name: 'StationFrontAttendance',
	data() {
		return {
			startTime: '',
			endTime: '',
			timer: null,
			nowTimestamp: null,
			nTime: '',
			locationId: null,
			address: null,
			subLng: null,
			subLat: null,
			userType: null,
			punchInInfo: {}, // 打卡信息记录
			pageHide: false, // 页面隐藏
			showWork: true,
			polygons: [], // 打卡区域
			isIn: false, // 是否在打卡范围内
			punchArea: ''
		}
	},
	computed: {
		...mapState({
			nickName: state => state.user.nickName,
			deptName: state => state.user.deptName,
			userId: state => state.user.userId,
			deptId: state => state.user.deptId
		}),
		addressText() {
			if (!this.address) return '';

			let address = '';
			if (this.address.city && this.address.city !== '未知') {
				address += this.address.city;
			}
			if (this.address.district && this.address.district !== '未知') {
				address += this.address.district;
			}
			if (this.address.street && this.address.street !== '未知') {
				address += this.address.street;
			}
			if (this.address.streetNum) {
				address += this.address.streetNum;
			}
			if (this.address.poiName && !address.includes(this.address.poiName)) {
				address += this.address.poiName;
			}

			// 如果地址为空，展示经纬度
			if (!address && this.subLng && this.subLat) {
				address = `(${this.subLng.toFixed(6)}, ${this.subLat.toFixed(6)})`;
			}

			return address;
		}
	},
	methods: {
		// 获取打卡状态名称
		getPunchName(status = 8) {
			const names = {
				0: '缺卡',
				1: '正常',
				2: '迟到',
				3: '早退',
				4: '定位异常',
				5: '迟到及定位异常',
				6: '早退及定位异常',
				7: '请假'
			}
			return names[status] ? names[status] : '缺卡'
		},

		// 设置时间
		setTime() {
			this.nowTimestamp = new Date().getTime();
			this.nTime = timeFormat(this.nowTimestamp, 'hh:MM:ss');
		},

		// 设置时间间隔
		setTimeInterval() {
			clearInterval(this.timer);
			this.timer = setInterval(() => {
				this.setTime();
			}, 1000);
		},

		// 刷新位置
		reLocation() {
			this.$toast.loading({
				message: '正在获取定位信息',
				forbidClick: true
			});

			navigator.geolocation.getCurrentPosition(
				position => {
					this.$toast.clear();
					this.subLng = position.coords.longitude;
					this.subLat = position.coords.latitude;
					// 调用地图API获取详细地址
					this.getAddressByCoords(this.subLng, this.subLat);
					this.isIn = this.isInArea();
				},
				error => {
					this.$toast.clear();
					this.$toast.fail('定位地址获取失败，请重试');
					this.subLng = null;
					this.subLat = null;
					this.address = null;
					this.isIn = this.isInArea();
				},
				{
					enableHighAccuracy: true,
					timeout: 10000,
					maximumAge: 5000
				}
			);
		},

		// 根据经纬度获取地址信息（使用高德地图API）
		getAddressByCoords(lon, lat) {
			this.$toast.loading({
				message: '获取地址信息...',
				duration: 0
			});

			// 创建高德地图API脚本，如果已存在则直接使用
			if (window.AMap) {
				this.convertCoordsToAddress(lon, lat);
				return;
			}

			// 如果没有加载过高德地图API，则先加载
			const script = document.createElement('script');
			script.type = 'text/javascript';
			script.src = 'https://webapi.amap.com/maps?v=2.0&key=b7a3da85edcdeecd9ae06c1a80f150c0&security=1';

			// 添加安全密钥配置
			window._AMapSecurityConfig = {
				securityJsCode: '758307c13c7a89718b29b7322c7b1bee'
			};

			script.onload = () => {
				this.convertCoordsToAddress(lon, lat);
			};
			script.onerror = () => {
				this.$toast.clear();
				this.$toast.fail('地图加载失败');
				this.address = {
					city: '未知',
					district: '未知',
					street: '未知',
					streetNum: '',
					poiName: `(${lon.toFixed(6)}, ${lat.toFixed(6)})`
				};
			};
			document.head.appendChild(script);
		},

		// 使用高德地图API进行坐标转换和地址解析
		convertCoordsToAddress(lon, lat) {
			// 使用高德地图的地理编码功能
			window.AMap.plugin('AMap.Geocoder', () => {
				const geocoder = new window.AMap.Geocoder();
				const lnglat = [lon, lat];

				geocoder.getAddress(lnglat, (status, result) => {
					this.$toast.clear();

					if (status === 'complete' && result.info === 'OK') {
						// 解析成功，获取地址
						if (result.regeocode && result.regeocode.addressComponent) {
							const addressComp = result.regeocode.addressComponent;
							this.address = {
								city: addressComp.city || addressComp.province,
								district: addressComp.district || '',
								street: addressComp.township || addressComp.street || '',
								streetNum: addressComp.streetNumber || '',
								poiName: result.regeocode.pois && result.regeocode.pois.length > 0
									? result.regeocode.pois[0].name
									: result.regeocode.formattedAddress || ''
							};
							console.log('地址解析成功:', this.address);
						} else {
							this.address = {
								city: '未知',
								district: '未知',
								street: '未知',
								streetNum: '',
								poiName: `(${lon.toFixed(6)}, ${lat.toFixed(6)})`
							};
						}
					} else {
						// 解析失败，使用默认格式
						console.error('地址解析失败:', result);
						this.address = {
							city: '未知',
							district: '未知',
							street: '未知',
							streetNum: '',
							poiName: `(${lon.toFixed(6)}, ${lat.toFixed(6)})`
						};
						this.$toast.fail('地址解析失败');
					}
					// 更新是否在打卡范围内的状态
					this.isIn = this.isInArea();
				});
			});
		},

		// 更新打卡
		handleUpdateClock(punchType) {
			this.$dialog.confirm({
				title: '提示',
				message: '是否确认更新打卡？'
			}).then(() => {
				this.handleClock(punchType);
			}).catch(() => {
				// 取消操作
			});
		},

		// 执行打卡
		handleClock(punchType) {
			let params = {
				type: this.userType,
				userId: this.userId,
				username: this.nickName,
				punchType: punchType,
				positionStatus: this.isIn ? 0 : 1
			};

			if (!this.subLng || !this.subLat) {
				this.$dialog.alert({
					title: '提示',
					message: '未获取到经纬度信息，请确认是否开启定位'
				});
				return;
			} else if (!this.isIn) {
				this.$dialog.alert({
					title: '提示',
					message: '当前未在打卡区域内，请在打卡区域内打卡'
				});
				return;
			}

			if (punchType == 0) {
				// 上班打卡
				params.startLongitude = this.subLng;
				params.startLatitude = this.subLat;
				params.startAddr = this.addressText;
			} else {
				// 下班打卡
				params.endLongitude = this.subLng;
				params.endLatitude = this.subLat;
				params.endAddr = this.addressText;
			}

			const punchInFn = this.punchInInfo.punchId ? editPunchIn : addPunchIn;
			if (this.punchInInfo.punchId) {
				params.punchId = this.punchInInfo.punchId;
			} else {
				params.punchTime = timeFormat(this.nowTimestamp, 'yyyy-MM-dd');
			}

			this.$toast.loading({
				message: '正在打卡',
				forbidClick: true
			});

			punchInFn(params).then(res => {
				this.$toast.clear();
				this.$toast.success({
					message: '打卡成功',
					onClose: () => {
						this.initData();
					}
				});
			}).catch(() => {
				this.$toast.clear();
				this.$toast.fail('打卡失败，请退出重新打卡');
			});
		},

		// 判断点是否在多边形内
		isPointInPolygon(polygon, lng, lat) {
			var numberOfPoints = polygon.length;
			var polygonLats = [];
			var polygonLngs = [];
			for (var i = 0; i < numberOfPoints; i++) {
				polygonLats.push(polygon[i]['lat']);
				polygonLngs.push(polygon[i]['lng']);
			}

			var polygonContainsPoint = false;
			for (var node = 0, altNode = (numberOfPoints - 1); node < numberOfPoints; altNode = node++) {
				if ((polygonLngs[node] > lng != (polygonLngs[altNode] > lng)) &&
					(lat < (polygonLats[altNode] - polygonLats[node]) *
						(lng - polygonLngs[node]) /
						(polygonLngs[altNode] - polygonLngs[node]) +
						polygonLats[node]
					)
				) {
					polygonContainsPoint = !polygonContainsPoint;
				}
			}

			return polygonContainsPoint;
		},

		// 判断是否在考勤区域内
		isInArea() {
			if (!this.subLng || !this.subLat) return false;
			if (this.polygons.length) {
				let isIn = false;
				for(let i=0; i<this.polygons.length; i++) {
					isIn = this.isPointInPolygon(this.polygons[i], this.subLng, this.subLat);
					if (isIn) break;
				}
				return isIn;
			} else {
				/* 未规定打卡区域。默认为正常 */
				return true;
			}
		},

		// 初始化数据
		async initData() {
			this.$toast.loading({
				message: '数据加载中',
				forbidClick: true
			});

			try {
				// 根据用户类型调用不同的API
				const api = this.userType == '0' ? getOfficeTodayInfo : getGridTodayInfo;
				const punchInfoRes = await api(this.userId);

				this.$toast.clear();

				// 打卡记录信息
				if (punchInfoRes.data) {
					punchInfoRes.data.startInTime = punchInfoRes.data.actualStartTime ? punchInfoRes.data.actualStartTime.substr(10, 6) : '';
					punchInfoRes.data.endOutTime = punchInfoRes.data.actualEndTime ? punchInfoRes.data.actualEndTime.substr(10, 6) : '';
					this.punchInInfo = punchInfoRes.data;

					// 设置上下班时间
					this.startTime = punchInfoRes.data.startTime ? punchInfoRes.data.startTime.substr(10, 6) : '';
					this.endTime = punchInfoRes.data.endTime ? punchInfoRes.data.endTime.substr(10, 6) : '';

					// 判断是否显示上班打卡按钮
					if (this.startTime) {
						const startTimeTr = new Date(`2021/07/23 ${this.startTime}:00`).getTime();
						const nowTimeTr = new Date(`2021/07/23 ${this.nTime}`).getTime();
						if (nowTimeTr - startTimeTr > 14400000) {
							// 当前时间超过打卡时间4小时，不再显示上班打卡
							this.showWork = false;
							/* 超出上午打卡范围，但是没有上午打卡状态，默认设置为缺卡 */
							if (!this.punchInInfo.startStatus) this.punchInInfo.startStatus = '0';
						}
					}

					// 设置打卡区域
					if (punchInfoRes.data.punchArea) {
						this.polygons = punchInfoRes.data.punchArea.split('/').map(poy => {
							return poy.split(';').map(lnglat => {
								const ll = lnglat.split(',');
								return { lng: parseFloat(ll[0]), lat: parseFloat(ll[1]) };
							});
						});
						this.punchArea = punchInfoRes.data.punchArea;
					}
				} else {
					this.$toast.fail('未获取到打卡信息');
				}

				// 获取当前位置
				this.reLocation();
			} catch (error) {
				this.$toast.clear();
				this.$toast.fail('数据获取失败，请退出重试');
			}
		}
	},
	created() {
		this.userType = this.$route.query.userType || 1;
	},
	mounted() {
		this.setTime();
		this.setTimeInterval();
		this.initData();
	},
	beforeDestroy() {
		clearInterval(this.timer);
	},
	activated() {
		if (this.pageHide) {
			this.pageHide = false;
			this.setTime();
			this.setTimeInterval();
			this.reLocation();
		}
	},
	deactivated() {
		this.pageHide = true;
		clearInterval(this.timer);
	}
}
</script>

<style lang="scss" scoped>
.station-clock-in {
	min-height: 100vh;
	background-color: #f7f8fa;
	padding: 16px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.user-info {
	display: flex;
	align-items: center;
	padding: 16px;
	background-color: #fff;
	border-radius: 8px;
	width: 100%;
	margin-bottom: 16px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-top: 50px;

	.avatar {
		width: 48px;
		height: 48px;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #1989fa;
		color: #fff;
		font-size: 16px;
		border-radius: 8px;
		margin-right: 12px;
	}

	.user-detail {
		flex: 1;

		.name {
			font-size: 16px;
			font-weight: 500;
			color: #323233;
			margin-bottom: 4px;
		}

		.dept {
			font-size: 13px;
			color: #969799;
		}
	}
}

.clock-main {
  width: 100%;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  .clock-status {
    display: flex;
    width: 100%;
    margin-bottom: 24px;

    .clock-item {
      flex: 1;
      background-color: #f5f5f5;
      border-radius: 8px;
      padding: 16px;
      text-align: left;

      &:first-child {
        margin-right: 12px;
      }

      .clock-title {
        font-size: 15px;
        color: #323233;
        margin-bottom: 8px;
      }

      .clock-time {
        font-size: 12px;
        color: #6B7280;

        .status-tag {
          display: inline-block;
          border: 1px solid #19BE6B;
          color: #19BE6B;
          font-size: 12px;
          padding: 0 4px;
          border-radius: 2px;
          transform: scale(0.8);
          margin-left: 5px;

          &.wrong {
            border-color: #ffb100;
            color: #ffb100;
          }
        }

        .update-btn {
          color: #1989fa;
          margin-left: 5px;
          font-size: 12px;
        }
      }
    }
  }

  .clock-btn-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin: 150px 0 130px 0;
  }
}

.clock-btn {
	width: 160px;
	height: 160px;
	border-radius: 50%;
	background-color: #1989fa;
	color: #fff;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
	margin-bottom: 32px;
	cursor: pointer;
	transition: transform 0.2s, box-shadow 0.2s;

	.btn-text {
		font-size: 18px;
		font-weight: 500;
		margin-bottom: 6px;
	}

	.btn-date {
		font-size: 14px;
		opacity: 0.9;
	}

	&:active {
		transform: scale(0.95);
		box-shadow: 0 2px 6px rgba(25, 137, 250, 0.3);
	}
}

.location-status {
	width: 100%;

	.status-item {
		display: flex;
		align-items: center;
		font-size: 14px;
		color: #666;
		margin-bottom: 12px;

		.van-icon {
			margin-right: 8px;
			flex-shrink: 0;
		}
	}

	.location-area {
		color: #323233;
		font-weight: 500;
		justify-content: center;
		margin-bottom: 16px;
	}

	.address-item {
		display: flex;
		align-items: flex-start;
		padding: 0 12px;

		.address-text {
			flex: 1;
			line-height: 1.4;
			word-break: break-all;
		}

		.refresh-btn {
			color: #1989fa;
			margin-left: 8px;
			flex-shrink: 0;
		}
	}
}

.current-time {
	margin-top: auto;
	width: 100%;
	display: flex;
	justify-content: center;
	padding: 20px 0;

	.time-display {
		font-size: 18px;
		color: #323233;
		font-weight: 500;
		padding: 8px 16px;
		background-color: #f5f5f5;
		border-radius: 20px;
	}
}
</style>
