<!--我要爆料-->
<template>
  <div class="problem-report">
    <!-- 导航栏 -->
    <van-nav-bar title="我要爆料">
      <template #right>
        <div class="nav-right">
          <span class="save-draft" @click="historyPage">上报记录</span>
        </div>
      </template>
    </van-nav-bar>

    <!-- 表单内容 -->
    <div class="form-content">
      <!-- 基本信息 -->
      <van-cell-group class="form-group">
        <!-- 问题分类选择 -->
        <van-field size="large" label="问题分类" readonly>
          <template #input>
            <custom-picker
              v-model="type2id"
              :columns="columns.type2Options"
              placeholder="请选择"
            />
          </template>
        </van-field>
      </van-cell-group>

      <van-cell-group class="form-group">
        <!-- 位置信息 -->
        <van-field
          size="large"
          label="位置选择"
          readonly
          placeholder=""
          right-icon="arrow"
        >
          <template #right-icon>
            <div class="section-content">
              <van-button
                plain
                type="primary"
                size="small"
                class="action-btn"
                @click="showMapDialog = true"
                >{{ formData.address ? "已定位" : "选择定位" }}</van-button
              >
            </div>
          </template>
        </van-field>

        <!-- 位置信息 -->
        <van-field
          size="large"
          label="位置描述"
          readonly
          placeholder=""
          right-icon="arrow"
        >
          <template #right-icon>
            <div class="section-content">
              <van-button
                plain
                type="primary"
                size="small"
                class="action-btn"
                >{{ formData.address ? "已生成" : "自动生成" }}</van-button
              >
            </div>
          </template>
        </van-field>

        <van-field
          type="textarea"
          v-model="formData.address"
          label=""
          placeholder="请输入位置描述"
          rows="2"
          autosize
          maxlength="50"
          show-word-limit
        />

        <!-- 问题描述 -->
        <van-field
          size="large"
          label="问题描述"
          readonly
          placeholder=""
          right-icon="arrow"
        >
          <template #right-icon>
            <div class="section-content">
              <idioms-selector @select="selectIdiom"
                >选择惯用语</idioms-selector
              >
            </div>
          </template>
        </van-field>
        <van-field
          type="textarea"
          v-model="formData.eventdesc"
          label=""
          placeholder="请输入问题描述"
          rows="2"
          autosize
          maxlength="50"
          show-word-limit
        />

        <!-- 图片上传 -->
        <div class="form-section">
          <div class="section-label">图片选择（最少一张）</div>
        </div>
        <div class="uploader-wrapper">
          <vantFileUpload
            v-model="formData.fileStr"
            :file-type="['jpg', 'png']"
          />
        </div>

        <!-- 联系方式 -->
        <van-field
          size="large"
          label="联系方式"
          readonly
          placeholder=""
          right-icon="arrow"
        >
          <template #right-icon>
            <div class="section-content"></div>
          </template>
        </van-field>
        <van-field
          v-model="formData.reporterphone"
          label=""
          placeholder="请输入联系方式"
          rows="2"
          autosize
          show-word-limit
        />
      </van-cell-group>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-btn-wrapper">
      <van-button type="info" block @click="onSubmit">上报</van-button>
    </div>
    <!-- 地图弹窗 -->
    <van-popup
      v-model="showMapDialog"
      position="bottom"
      :style="{ height: '80%', width: '100%' }"
    >
      <div class="map-dialog">
        <Map
          v-if="showMapDialog"
          ref="mapComponent"
          @locationSelected="handleLocationSelected"
        />
        <div class="map-dialog-footer">
          <van-button type="info" block @click="confirmLocation"
            >确认位置</van-button
          >
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import CustomPicker from "@/components/CustomPicker/index.vue";
import TabSwitch from "@/components/TabSwitch/index.vue";
import Map from "@/components/map/index.vue";
import vantFileUpload from "@/components/vant-file-upload";
import IdiomsSelector from "@/components/IdiomsSelector";
import { myRevelation } from "@/api/common";
import { login, getInfo } from "@/api/login";
import { setToken, getToken } from "@/util/auth";

export default {
  name: "ProblemReport",
  mixins: [],
  components: {
    CustomPicker,
    TabSwitch,
    Map,
    vantFileUpload,
    IdiomsSelector,
  },
  data() {
    return {
      showMapDialog: false, // 新增弹窗控制状态
      type2id: "",
      // 表单数据
      formData: {
        source: "",
        createid: "",
        areaid: "", //定位获取所属区县
        streetid: "", //定位获取所属街道
        address: "", //位置描述
        eventdesc: "", //问题描述
        reporterphone: "", //联系方式
        fileStr: "",
        x84: "",
        y84: "",
      },

      // 选择器数据
      columns: {
        type2Options: [
          { text: "市容环境类", value: "市容环境类" },
          { text: "街面秩序类", value: "街面秩序类" },
          { text: "公共设施类", value: "公共设施类" },
          { text: "突发事件类", value: "突发事件类" },
          { text: "牛皮癣", value: "牛皮癣" },
          { text: "其他", value: "其他" },
        ],
      },
      areaOptions: [],
    };
  },
  created() {},
  mounted() {
    //网页授权回调 携带code
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    if (params.get("code")) {
      //这里走你的登录逻辑或者干其他的啥随你遍咯
      let code = params.get("code");
      console.log(code, "2222code");
      const token = getToken();
      if (!token) {
        this.loginWx(code);
      }
    }
  },
  methods: {
    loginWx(code) {
      login({ wxCode: code })
        .then((res) => {
          setToken(res.token);
          this.GetInfo(res.token);
          this.getDictsList();
        })
        .catch((error) => {
          this.$toast.fail(error.msg);
        });
    },
    GetInfo(token) {
      getInfo(token).then((res) => {
        const user = res.user;
        const userObj = {
          deptName: user?.dept?.deptName || "",
          deptId: user?.dept?.deptId || "",
          nickName: user.nickName,
          ...user,
          id: res?.dogUserQualifi?.id || "",
          realName: user.userName || "",
          mobile: user.phonenumber || "",
          userQualifi: res?.dogUserQualifi || undefined,
          qualifi: res?.dogQualifi || undefined,
          roleType: res.roleType,
          roles: res.roles,
          userType: user.userType,
          userId: user.userId,
        };
        this.vuex("user", userObj);
        this.initParams();
      });
    },
    historyPage() {
      this.$router.push("/wxNewsHistory");
    },

    initParams() {
      this.formData.source = "3"; //移动端默认微信上报
      this.formData.createid = this.user.nickName;
    },

    //初始化字典表
    getDictsList() {
      //区划
      this.getDicts("county").then((response) => {
        this.areaOptions = response.data.map((item) => ({
          label: item.dictLabel,
          value: item.dictValue,
        }));
      });
      this.type2id = this.columns.type2Options[0].value;
    },

    // 选择器确认
    onPickerConfirm(type, value) {
      console.log(`${type} selected:`, value);
    },

    handleLocationSelected(e) {
      console.log(e);
      this.formData.address = e.formatted_address;
      this.formData.x84 = e.location.lon;
      this.formData.y84 = e.location.lat;
      this.formData.areaid = this.areaOptions.find(
        (item) => item.label == e.addressComponent.county
      ).value
        ? this.areaOptions.find(
            (item) => item.label == e.addressComponent.county
          ).value
        : "";
      this.formData.streetid = e.addressComponent.road;
    },
    confirmLocation() {
      this.showMapDialog = false;
    },
    // 提交表单
    onSubmit() {
      // 表单验证
      if (!this.type2id) {
        return this.$toast("请选择问题类型");
      }
      if (!this.formData.address) {
        return this.$toast("请输入位置描述");
      }
      if (!this.formData.eventdesc) {
        return this.$toast("请输入问题描述");
      }
      if (this.formData.fileStr.length === 0) {
        return this.$toast("请至少上传一张图片");
      }
      if (!this.formData.reporterphone) {
        return this.$toast("请输入联系方式");
      }

      // 提交表单
      this.$toast.loading({
        message: "提交中...",
        forbidClick: true,
      });

      let formData = JSON.parse(JSON.stringify(this.formData));
      formData.reporterid = this.user.nickName;
      myRevelation(formData).then((res) => {
        if (res.code == 200) {
          this.$toast.success("提交成功");
          this.resetForm();
        }
      });
    },

    resetForm() {
      this.formData = {
        source: "",
        createid: "",
        areaid: "", //定位获取所属区县
        streetid: "", //定位获取所属街道
        address: "", //位置描述
        eventdesc: "", //问题描述
        reporterphone: "", //联系方式
        fileStr: "",
      };
    },

    // 选择惯用语
    selectIdiom(content) {
      if (this.formData.eventdesc)
        this.formData.eventdesc = this.formData.eventdesc.split(":")[1];
      this.formData.eventdesc = "[" + this.type2id + "]:" + content;
    },
  },
  watch: {
    type2id(val) {
      if (this.formData.eventdesc)
        this.formData.eventdesc = this.formData.eventdesc.split(":")[1];
      this.formData.eventdesc = "[" + val + "]:" + this.formData.eventdesc;
    },
  },
};
</script>

<style lang="scss" scoped>
.problem-report {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 60px;
}

// 表单内容样式
.form-content {
  padding-bottom: 20px;
}

.form-group {
  background-color: #fff;
  margin-bottom: 10px;
}

// 自定义 van-field 样式
::v-deep .van-field {
  font-size: 14px;
}

::v-deep .van-field__label {
  width: 90px;
  color: #333;
}

::v-deep .van-field__placeholder {
  color: #999;
}

::v-deep .van-field__right-icon {
  color: #ccc;
}

// 表单区块样式
.form-section {
  padding: 0 15px;
  margin-top: 15px;

  .section-label {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
  }

  .section-content {
    display: flex;
    justify-content: flex-end;
  }
}

// 按钮样式
.action-btn {
  height: 32px;
  padding: 0 15px;
  font-size: 13px;
  border-radius: 4px;
  background-color: #e8f3ff;
  border-color: #e8f3ff;
  color: #1989fa;
}

// 自动生成文本样式
.auto-text {
  padding: 0 15px;
  font-size: 14px;
  color: #999;
  margin-top: 5px;
}

// 文本域样式
.textarea-wrapper {
  padding: 0 15px;
  margin-top: 10px;
}

.problem-textarea {
  background-color: #fff;
  border-radius: 4px;

  ::v-deep .van-field__control {
    min-height: 80px;
  }
}

// 上传组件样式
.uploader-wrapper {
  padding: 0 15px;

  ::v-deep .van-uploader__upload {
    background-color: #f7f8fa;
    border: 1px dashed #dcdee0;
  }
}

// 是否自办结样式
.self-complete {
  display: flex;
  align-items: center;
  margin-top: 20px;

  .section-label {
    margin-bottom: 0;
    margin-right: 20px;
  }

  .radio-wrapper {
    flex: 1;
  }

  ::v-deep .van-radio {
    margin-right: 20px;
  }

  ::v-deep .van-radio__icon--checked .van-icon {
    background-color: #1989fa;
    border-color: #1989fa;
  }

  ::v-deep .van-radio__icon {
    font-size: 16px;
  }
}

// 提交按钮样式
.submit-btn-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 10px 15px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  ::v-deep .van-button {
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    border-radius: 4px;
  }
}

// 适配 iPhone X 等带底部安全区域的机型
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .submit-btn-wrapper {
    padding-bottom: calc(10px + constant(safe-area-inset-bottom));
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .submit-btn-wrapper {
    padding-bottom: calc(10px + env(safe-area-inset-bottom));
  }
}

// 自定义选择器样式
::v-deep .van-field__input {
  .custom-picker {
    width: 100%;
    text-align: right;
  }

  .selected-value {
    color: #323233;
    font-size: 14px;
  }
}

// 新增地图弹窗样式
.map-dialog {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-footer {
    padding: 10px;
    background: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }
}

// 调整地图容器高度
.MapContainer {
  height: 0; // 隐藏底部固定地图
}

// 导航栏右侧按钮样式
.nav-right {
  display: flex;
  align-items: center;

  .save-draft {
    font-size: 14px;
    color: #1989fa;
  }
}
</style>
