import Cookies from 'js-cookie'
import {isInZLBEnvironment} from "@/util/index";
const TokenKey = 'YGF-MOBILE-Token'

export function getToken() {
  const isZLB = isInZLBEnvironment()
  console.log('getToken - 环境检测:', isZLB ? '浙里办环境' : '普通环境')

  if (isZLB) {
    return ZlbGetToken()
  } else {
    const token = Cookies.get(TokenKey)
    console.log('getToken - Cookie token:', token)
    return token
  }
}

export function setToken(token) {
  return isInZLBEnvironment()? ZlbSetToken(token):Cookies.set(TokenKey, token)
}

export function removeToken() {
  return isInZLBEnvironment()? ZlbDeleToken():Cookies.remove(TokenKey)
}

function ZlbGetToken() {
  return new Promise((resolve, reject) => {
    // 检查ZWJSBridge是否可用
    if (!window.ZWJSBridge) {
      console.error('ZWJSBridge不可用')
      reject(new Error('ZWJSBridge不可用'))
      return
    }

    if (!window.ZWJSBridge.getLocalStorage) {
      console.error('ZWJSBridge.getLocalStorage方法不存在')
      reject(new Error('getLocalStorage方法不存在'))
      return
    }

    console.log('ZlbGetToken - 开始获取token, key:', TokenKey)

    window.ZWJSBridge.getLocalStorage({
      key: TokenKey,
    }).then((data) => {
      console.log('ZlbGetToken - 获取到token对象, key:', data)
      resolve(data)
    })
  })
}

function ZlbSetToken(token) {
  window.ZWJSBridge.setLocalStorage({
    key: TokenKey,
    value: token
  })
}

function ZlbDeleToken() {
  window.ZWJSBridge.removeLocalStorage({
    key: TokenKey
  })
}
