<template>
  <div class="container" :class="elderModeClass">
    <!-- 头部区域 -->
    <div class="container-head">
      <div class="head_box">
        <img src="@/assets/personCenter/head_img.png" alt="" />
      </div>
      <div class="name">{{name}}</div>
      <div class="des">{{desc}}</div>
    </div>
    <div class="list_box">
      <div class="list_item" v-for="(item, index) in listData" :key="index" @click="navClick(item)">
        <div class="item_left">
          <img :src="item.icon" alt="" />
          <div class="title">{{ item.name }}</div>
        </div>
        <van-icon name="arrow" color="#9CA3AF" />
      </div>
    </div>
    <van-button class="btn" type="info" @click="handleLogout">退出登录</van-button>
  </div>
</template>

<script>
import {removeToken} from "@/util/auth";
import elderMode from "@/mixins/elderMode";

export default {
  name: "PersonCenter",
  mixins: [elderMode],
  components: {},
  props: {},
  data() {
    return {
      name:"",
      desc:"",
      listData: [],
    };
  },
  watch: {},
  mounted() {
    this.initMenu()
    setTimeout(() => {
      this.initData()
    },500)
  },
  computed: {
    elderModeText() {
      const currentMode = this.vuex_uiStyle || 'normal'
      return currentMode === 'elder' ? '标准版' : '长辈版'
    },
  },
  methods: {
    initMenu() {
      this.listData = [
        {
          id: "1",
          name: "数据同步",
          icon: require("@/assets/personCenter/DataSynchronization.png"),
          action: "syncData"
        },
        {
          id: "2",
          name: "清除缓存",
          icon: require("@/assets/personCenter/ClearCache.png"),
          action: "clearCache"
        },
        {
          id: "3",
          name: "惯用语设置",
          icon: require("@/assets/personCenter/IdiomSetting.png"),
          url: "/IdiomaticExpressions",
        },
        {
          id: "4",
          name: "今日统计",
          icon: require("@/assets/personCenter/TodayStatistics.png"),
          url: "/TodayStatistics"
        },
        {
          id: "5",
          name: this.elderModeText,
          icon: require("@/assets/personCenter/help.png"),
          action: "toggleElderMode"
        },
        {
          id: "6",
          name: "帮助",
          icon: require("@/assets/personCenter/help.png")
        },
      ];
    },
    initData() {
      this.name = this.user.realName
      this.desc = this.user.nickName
    },
    // 退出登录处理
    handleLogout() {
      this.$dialog.confirm({
        title: '退出登录',
        message: '确定要退出登录吗？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        // 用户点击确定，执行退出登录操作
        this.$toast.loading({
          message: '退出中...',
          forbidClick: true,
          duration: 0
        });

        // 清除token和用户信息
        removeToken()
        this.vuex('user', null)

        // 模拟接口调用延迟
        setTimeout(() => {
          this.$toast.clear();
          this.$toast.success('退出成功');
          // 跳转到登录页
          this.$router.replace('/login');
        }, 1000);
      }).catch(() => {
        // 用户点击取消，不做任何操作
      });
    },
    //菜单点击
    navClick(item) {
      // 如果有特定的action方法，则执行对应方法
      if (item.action && this[item.action]) {
        this[item.action]();
        return;
      }

      // 如果有url则进行跳转
      if (item.url) {
        this.$router.push(item.url);
      }
    },

    // 切换适老化模式
    toggleElderMode() {
      const currentMode = this.vuex_uiStyle || 'normal';
      const newMode = currentMode === 'elder' ? 'normal' : 'elder';

      console.log('当前模式:', currentMode, '目标模式:', newMode);

      this.$dialog.confirm({
        title: '切换显示模式',
        message: `确定要切换到${newMode === 'elder' ? '适老化' : '标准'}模式吗？\n\n${newMode === 'elder' ? '适老化模式将提供更大的字体、更高的对比度和更简化的界面。' : '标准模式将恢复默认的界面样式。'}`,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        console.log('用户确认切换到:', newMode);

        // 显示加载提示
        this.$toast.loading({
          message: '正在切换模式...',
          forbidClick: true,
          duration: 0
        });

        // 延迟执行切换，确保UI更新
        setTimeout(() => {
          this.vuex('vuex_uiStyle', newMode);

          // 验证切换结果
          setTimeout(() => {
            const actualMode = this.vuex_uiStyle;
            console.log('切换后的实际模式:', actualMode);
            this.initMenu()
            this.$toast.clear();

            if (actualMode === newMode) {
              this.$toast.success(`已成功切换到${newMode === 'elder' ? '适老化' : '标准'}模式`);
            } else {
              this.$toast.fail('模式切换失败，请重试');
            }
          }, 100);
        }, 200);

      }).catch(() => {
        console.log('用户取消切换');
      });
    },
    // 数据同步
    syncData() {
      this.$toast.loading({
        message: '数据同步中...',
        forbidClick: true,
        duration: 0
      });

      // 模拟同步延迟
      setTimeout(() => {
        this.$toast.clear();
        this.$toast.success('数据同步成功');
      }, 2000);
    },
    // 清除缓存
    clearCache() {
      this.$toast.loading({
        message: '清除缓存中...',
        forbidClick: true,
        duration: 0
      });

      // 模拟清除缓存延迟
      setTimeout(() => {
        // 实际清除缓存逻辑可以在这里添加
        localStorage.clear();
        sessionStorage.clear();

        this.$toast.clear();
        this.$toast.success('缓存清除成功');
      }, 1500);
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #f5f5f5;
  text-align: center;
  .container-head {
    width: 100%;
    height: 220px;
    background-size: cover;
    background: url("~@/assets/personCenter/personCenter_head.png") no-repeat
      center top;
    background-size: 100% auto;
    z-index: 1;
    .head_box {
      text-align: center;
      padding-top: 54px;
      img {
        width: 72px;
        height: 72px;
      }
    }
    .name {
      margin-top: 10px;
      font-size: 16px;
      text-align: center;
      color: #ffffff;
      line-height: 21px;
    }
    .des {
      margin-top: 8px;
      font-size: 14px;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);
      line-height: 21px;
    }
  }
  .list_box {
    background: #fff;
    .list_item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      align-content: center;
      padding-left: 22px;
      padding-right: 16px;
      height: 53px;
      border-top: 2px solid #e5e7eb;
      &:first-child {
        border-top: none;
      }
      .item_left {
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-content: center;
        img {
          width: 17px;
          height: 17px;
          margin-right: 16px;
        }
      }
    }
  }
  .btn {
    position: fixed;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    width: 327px;
    height: 48px;
    background: #428ffc;
    border-radius: 8px ;
    font-family: Roboto, Roboto;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    line-height: 48px;
    text-align: center;
  }
}
</style>
