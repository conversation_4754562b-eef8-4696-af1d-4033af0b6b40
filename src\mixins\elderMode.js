// 适老化模式混入
import { forceResetStyles } from '@/utils/elderUtils'

export default {
  data() {
    return {
      isElderMode: false
    }
  },

  computed: {
    elderModeClass() {
      return this.isElderMode ? 'elder-mode' : ''
    }
  },

  mounted() {
    this.initElderMode()
    this.addElderModeListeners()
  },

  beforeDestroy() {
    this.removeElderModeListeners()
  },

  methods: {
    // 初始化适老化模式
    initElderMode() {
      // 从vuex获取UI样式
      const uiStyle = this.vuex_uiStyle || 'normal'
      this.isElderMode = uiStyle === 'elder'

      // 应用适老化样式
      this.applyElderMode()

      // 监听vuex状态变化
      this.$watch('vuex_uiStyle', (newVal) => {
        this.isElderMode = newVal === 'elder'
        this.applyElderMode()
      }, { immediate: true })
    },

    // 应用适老化模式
    applyElderMode() {
      const body = document.body
      const html = document.documentElement

      if (this.isElderMode) {
        console.log('应用适老化模式...')

        // 先清理之前的样式（避免重复应用）
        if (!body.classList.contains('elder-mode')) {
          this.restoreDefaultStyles()
        }

        // 添加适老化样式类
        body.classList.add('elder-mode')
        html.classList.add('elder-mode')

        // 设置字体大小
        this.setElderFontSize()

        // 设置高对比度
        // this.setHighContrast()

        // 简化动画
        this.simplifyAnimations()

        // 增强焦点样式
        // this.enhanceFocus()

        console.log('适老化模式已应用')

      } else {
        console.log('恢复标准模式...')

        // 移除适老化样式类
        body.classList.remove('elder-mode')
        html.classList.remove('elder-mode')

        // 恢复默认样式
        this.restoreDefaultStyles()

        console.log('标准模式已恢复')
      }
    },

    // 设置适老化字体大小（符合浙里办规范）
    setElderFontSize() {
      const html = document.documentElement
      const elderFontSize = 20 // 浙里办推荐字体大小

      html.style.fontSize = `${elderFontSize}px`

      // 设置CSS变量
      html.style.setProperty('--elder-font-size-base', `${elderFontSize}px`)
      html.style.setProperty('--elder-font-size-large', '22px')
      html.style.setProperty('--elder-font-size-small', '18px')
      html.style.setProperty('--elder-line-height', '1.8')

      // 调整rem基准值
      if (window.setRem) {
        window.setRem()
      }
    },

    // 设置高对比度（符合浙里办WCAG AA标准）
    setHighContrast() {
      const style = document.createElement('style')
      style.id = 'elder-high-contrast'
      style.textContent = `
        .elder-mode {
          --elder-contrast-ratio: 7; /* WCAG AAA级对比度 */
        }
        .elder-mode * {
          text-shadow: none !important;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }
        .elder-mode .van-button,
        .elder-mode button {
          box-shadow: 0 2px 4px rgba(0, 82, 217, 0.3) !important;
        }
        .elder-mode .van-card,
        .elder-mode .card-item,
        .elder-mode .record-item {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }
      `

      if (!document.getElementById('elder-high-contrast')) {
        document.head.appendChild(style)
      }
    },

    // 简化动画
    simplifyAnimations() {
      const style = document.createElement('style')
      style.id = 'elder-no-animations'
      style.textContent = `
        .elder-mode *,
        .elder-mode *::before,
        .elder-mode *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
          transition-delay: 0ms !important;
        }
      `

      if (!document.getElementById('elder-no-animations')) {
        document.head.appendChild(style)
      }
    },

    // 增强焦点样式（符合浙里办无障碍规范）
    enhanceFocus() {
      const style = document.createElement('style')
      style.id = 'elder-focus-enhancement'
      style.textContent = `
        .elder-mode *:focus {
          outline: 4px solid #0052D9 !important;
          outline-offset: 2px !important;
          box-shadow: 0 0 0 8px rgba(0, 82, 217, 0.2) !important;
          z-index: 999 !important;
        }
        .elder-mode button:focus,
        .elder-mode .van-button:focus {
          transform: scale(1.05) !important;
          box-shadow: 0 0 0 4px #0052D9, 0 0 0 8px rgba(0, 82, 217, 0.2) !important;
        }
        .elder-mode input:focus,
        .elder-mode textarea:focus,
        .elder-mode .van-field__control:focus {
          border-color: #0052D9 !important;
          border-width: 3px !important;
          box-shadow: 0 0 0 4px rgba(0, 82, 217, 0.2) !important;
        }
        .elder-mode a:focus {
          background-color: rgba(0, 82, 217, 0.1) !important;
          text-decoration: underline !important;
          text-decoration-thickness: 3px !important;
        }
      `

      if (!document.getElementById('elder-focus-enhancement')) {
        document.head.appendChild(style)
      }
    },

    // 恢复默认样式
    restoreDefaultStyles() {
      // 使用强制重置方法确保完全恢复
      forceResetStyles()
    },

    // 添加适老化事件监听
    addElderModeListeners() {
      // 监听浙里办UI样式变化
      if (window.ZWJSBridge && window.ZWJSBridge.onReady) {
        window.ZWJSBridge.onReady(() => {
          if (window.ZWJSBridge.getUiStyle) {
            window.ZWJSBridge.getUiStyle({})
              .then(result => {
                if (result && result.uiStyle) {
                  this.vuex('vuex_uiStyle', result.uiStyle)
                }
              })
              .catch(error => {
                console.log('获取UI样式失败:', error)
              })
          }
        })
      }

      // 监听键盘事件（无障碍支持）
      document.addEventListener('keydown', this.handleKeyboardNavigation)
    },

    // 移除事件监听
    removeElderModeListeners() {
      document.removeEventListener('keydown', this.handleKeyboardNavigation)
    },

    // 键盘导航处理
    handleKeyboardNavigation(event) {
      if (!this.isElderMode) return

      // Tab键导航增强
      if (event.key === 'Tab') {
        const focusableElements = this.getFocusableElements()
        const currentIndex = focusableElements.indexOf(document.activeElement)

        if (event.shiftKey) {
          // Shift+Tab 向前导航
          const prevIndex = currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1
          focusableElements[prevIndex]?.focus()
        } else {
          // Tab 向后导航
          const nextIndex = currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0
          focusableElements[nextIndex]?.focus()
        }
      }

      // Enter键激活
      if (event.key === 'Enter' && document.activeElement) {
        const element = document.activeElement
        if (element.tagName === 'BUTTON' || element.classList.contains('van-button')) {
          element.click()
        }
      }
    },

    // 获取可聚焦元素
    getFocusableElements() {
      const selector = [
        'button',
        '[href]',
        'input',
        'select',
        'textarea',
        '[tabindex]:not([tabindex="-1"])',
        '.van-button',
        '.van-cell--clickable'
      ].join(',')

      return Array.from(document.querySelectorAll(selector))
        .filter(el => !el.disabled && el.offsetParent !== null)
    },

    // 切换适老化模式（用于测试）
    toggleElderMode() {
      const currentMode = this.vuex_uiStyle || 'normal'
      const newMode = currentMode === 'elder' ? 'normal' : 'elder'

      console.log('混入切换模式:', currentMode, '->', newMode)

      // 直接切换，不显示确认对话框（在具体页面中可以重写此方法）
      this.vuex('vuex_uiStyle', newMode)

      // 验证切换结果
      setTimeout(() => {
        const actualMode = this.vuex_uiStyle
        console.log('混入切换完成，当前模式:', actualMode)
      }, 100)
    },

    // 获取适老化状态文本
    getElderModeText() {
      return this.isElderMode ? '当前为适老化模式' : '当前为标准模式'
    }
  }
}
