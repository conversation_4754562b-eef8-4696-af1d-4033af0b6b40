import Vue from 'vue'
import {
  Tabbar,
  TabbarItem,
  Cell,
  CellGroup,
  Icon,
  Grid,
  GridItem,
  Image as VanImage,
  Popup,
  List,
  NavBar,
  Button,
  Form,
  Field,
  Picker,
  PullRefresh,
  Toast,
  Tab,
  Tabs,
  Dialog,
  Uploader,
  ImagePreview,
  Step,
  Steps,
  Loading,
  Search,
  Progress,
  DatetimePicker,
  CheckboxGroup,
  Checkbox,
  Radio,
  RadioGroup,
  Empty,
  Divider,
  Switch,
  ActionSheet,
  Col,
  Row,
  Swipe,
  SwipeItem,
  DropdownMenu,
  DropdownItem
} from 'vant'

Vue.use(Tabbar)
Vue.use(TabbarItem)
Vue.use(Cell)
Vue.use(CellGroup)
Vue.use(Icon)
Vue.use(Grid)
Vue.use(GridItem)
Vue.use(VanImage)
Vue.use(Popup)
Vue.use(List)
Vue.use(NavBar)
Vue.use(Button)
Vue.use(Form)
Vue.use(Field)
Vue.use(Picker)
Vue.use(PullRefresh)
Vue.use(Toast)
Vue.use(Tab)
Vue.use(Tabs)
Vue.use(Dialog)
Vue.use(Uploader)
Vue.use(ImagePreview)
Vue.use(Step)
Vue.use(Steps)
Vue.use(Loading)
Vue.use(Search)
Vue.use(Progress)
Vue.use(DatetimePicker)
Vue.use(CheckboxGroup)
Vue.use(Checkbox)
Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(Empty)
Vue.use(Divider)
Vue.use(Switch)
Vue.use(ActionSheet)
Vue.use(Col)
Vue.use(Row)
Vue.use(Swipe)
Vue.use(SwipeItem)
Vue.use(DropdownMenu)
Vue.use(DropdownItem)

// 全局注册Toast
Vue.prototype.$toast = Toast
// 全局注册ImagePreview
Vue.prototype.$imagePreview = ImagePreview
