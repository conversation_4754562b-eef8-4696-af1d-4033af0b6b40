<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-08 15:22:05
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-08 15:24:45
-->
<!--犬类扫一扫-->
<template>
  <div class="dog-scan">
    <van-cell-group inset>
      <van-cell
        :title="getScanTitle()"
        :is-link="!wxConfiguring"
        :clickable="!wxConfiguring"
        @click="onScan"
      >
        <template #icon>
          <img class="scan-icon" :src="scanIcon" alt="scan" />
        </template>
        <template #right-icon v-if="wxConfiguring">
          <van-loading size="16px" />
        </template>
      </van-cell>
    </van-cell-group>
  </div>
</template>

<script>
import { login, getInfo } from "@/api/login";
import { setToken, getToken } from "@/util/auth";
import { wxScanQRCode } from "@/util/wxJssdk";
import wxJssdkMixin from "@/mixins/wxJssdk";

export default {
  name: "dogScan",
  mixins: [wxJssdkMixin],
  components: {},
  data() {
    return {
      scanIcon: require("@/assets/wxPages/dog_scan.png"),
    };
  },
  computed: {
    isDevelopment() {
      return process.env.NODE_ENV === "development";
    },
  },
  created() {},
  mounted() {
    //网页授权回调 携带code
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    if (params.get("code")) {
      //这里走你的登录逻辑或者干其他的啥随你遍咯
      let code = params.get("code");
      console.log(code, "2222code");
      const token = getToken();
      if (!token) {
        this.loginWx(code);
      }
    }
    // 微信JSSDK初始化由混入自动处理

    // 监听微信JSSDK状态变化
    this.$on("wx-jssdk-ready", () => {
      console.log("微信JSSDK配置完成，可以使用微信功能");
      this.$toast.success("微信功能已就绪");
    });

    this.$on("wx-jssdk-error", (error) => {
      console.error("微信JSSDK配置失败:", error);
      // 可以在这里添加重试按钮或其他处理逻辑
    });
  },
  methods: {
    loginWx(code) {
      login({ wxCode: code })
        .then((res) => {
          setToken(res.token);
          this.GetInfo(res.token);
          this.getDictsList();
        })
        .catch((error) => {
          this.$toast.fail(error.msg);
        });
    },
    GetInfo(token) {
      getInfo(token).then((res) => {
        const user = res.user;
        const userObj = {
          deptName: user?.dept?.deptName || "",
          deptId: user?.dept?.deptId || "",
          nickName: user.nickName,
          ...user,
          id: res?.dogUserQualifi?.id || "",
          realName: user.userName || "",
          mobile: user.phonenumber || "",
          userQualifi: res?.dogUserQualifi || undefined,
          qualifi: res?.dogQualifi || undefined,
          roleType: res.roleType,
          roles: res.roles,
          userType: user.userType,
          userId: user.userId,
        };
        this.vuex("user", userObj);
        this.initParams();
      });
    },

    initParams() {
      this.formData.source = "3"; //移动端默认微信上报
      this.formData.createid = this.user.nickName;
    },

    //获取URL中的参数
    getUrlParams(url) {
      // 创建空对象存储参数
      let obj = "";
      if (url.split("?")[1]) {
        // 通过 ? 分割获取后面的参数字符串
        let urlStr = url.split("?")[0];
        let urlStr1 = url.split("?")[1];
        if (urlStr == "https://www.jinhuadog.com/pet") {
          obj = "https://ygf.xzzfj.jinhua.gov.cn/ygf/pet?" + urlStr1;
        }
      }
      return obj;
    },

    async onScan() {
      console.log("点击扫一扫按钮");

      // 检查是否在微信环境中
      if (!this.isInWechat()) {
        this.$toast.fail("请在微信中打开");
        return;
      }

      // 如果正在配置JSSDK，提示用户等待
      if (this.wxConfiguring) {
        this.$toast("正在配置微信功能，请稍候...");
        return;
      }

      // 显示加载提示
      const loadingToast = this.$toast.loading({
        message: "准备扫一扫...",
        forbidClick: true,
        duration: 0,
      });

      try {
        // 确保JSSDK已配置
        const isReady = await this.ensureWxJssdkReady(["scanQRCode"]);
        if (!isReady) {
          loadingToast.clear();

          // 提供重试选项
          this.$dialog
            .confirm({
              title: "微信功能初始化失败",
              message: "是否重新尝试初始化微信功能？",
              confirmButtonText: "重试",
              cancelButtonText: "取消",
            })
            .then(() => {
              this.resetAndInitWxJssdk(["scanQRCode"]);
            })
            .catch(() => {
              // 用户取消
            });
          return;
        }

        loadingToast.clear();

        // 调用扫一扫功能
        const result = await wxScanQRCode({
          needResult: 1,
          scanType: ["qrCode", "barCode"],
        });

        this.$toast.success("扫描成功");
        console.log("scan result:", result);

        // 处理扫描结果
        this.handleScanResult(result);
      } catch (error) {
        loadingToast.clear();
        console.error("扫描失败:", error);

        // 根据错误类型提供不同的提示
        let errorMessage = "扫描失败，请重试";
        if (error.message.includes("用户取消")) {
          errorMessage = "扫描已取消";
        } else if (error.message.includes("未配置")) {
          errorMessage = "微信功能未配置，请重试";
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.$toast.fail(errorMessage);
      }
    },

    // 处理扫描结果
    handleScanResult(result) {
      if (!result) {
        this.$toast.fail("扫描结果为空");
        return;
      }

      // 检查是否是URL格式的二维码
      if (this.getUrlParams(result)) {
        let url = this.getUrlParams(result);
        location.href = url;
      } else {
        // 其他类型的二维码处理
        console.log("扫描到的内容:", result);
        this.$toast.success(`扫描成功: ${result}`);
      }
    },

    // 获取扫一扫按钮标题
    getScanTitle() {
      if (this.wxConfiguring) {
        return "正在配置微信功能...";
      }
      if (!this.wxJssdkReady) {
        return "扫一扫（点击配置）";
      }
      return "扫一扫";
    },
  },
  watch: {},
};
</script>

<style lang="scss" scoped>
.dog-scan {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 15px;
  
}

/* 垂直居中 van-cell 内的图标、标题与右侧箭头 */
:deep(.van-cell) {
  align-items: center;
}
:deep(.van-cell__left-icon),
:deep(.van-cell__right-icon),
:deep(.van-cell__title) {
  display: flex;
  align-items: center;
}

.scan-icon {
  width: 40px;
  height: 40px;
  margin-right: 8px;
  border-radius: 6px;
}

.status-info {
  margin: 20px 15px 0;
}
</style>
