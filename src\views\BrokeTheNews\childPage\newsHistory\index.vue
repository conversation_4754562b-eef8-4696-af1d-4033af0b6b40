<template>
  <div class="news-history" :class="elderModeClass">
    <!-- 导航栏 -->
    <van-nav-bar
      title="我的上报记录"
      left-arrow
      fixed
      @click-left="onClickLeft"
    >
      <template #right>
        <div class="nav-right">
          <span class="save-draft" @click="onAddNew">新增</span>
        </div>
      </template>
    </van-nav-bar>

    <!-- 搜索和筛选 -->
    <div class="search-filter">
      <van-dropdown-menu>
        <van-dropdown-item v-model="filterType" :options="filterOptions" />
      </van-dropdown-menu>
      <van-search
        v-model="queryParams.keyWord"
        placeholder="请输入搜索关键词"
        shape="round"
        @search="onSearch"
      />
    </div>

    <!-- 记录列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoadMore"
        :immediate-check="false"
      >
        <div class="record-list">
          <!-- 待受理 -->
          <div class="record-item" v-for="(item, index) in list" :key="index" @click="viewDetail(item)">
            <div class="record-header">
              <div class="record-type">{{ item.type }}</div>
              <div class="record-status pending">{{getStatusText(item.status)}}</div>
            </div>
            <div class="record-content">
              <div class="record-desc">问题描述: {{ item.description }}</div>
              <div class="record-address">详细地址: {{ item.address }}</div>
            </div>
          </div>

          <!-- 空状态 -->
          <div class="empty-state" v-if="total === 0 && !loading && !refreshing">
            <van-empty description="暂无上报记录" />
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import { getNewsList } from "@/api/common";
import elderMode from "@/mixins/elderMode";

export default {
  name: "newsHistory",
  mixins: [elderMode],
  data() {
    return {
      isLoading: false,
      refreshing: false,
      loading: false,
      finished: false,
      filterType: '',
      filterOptions: [
        { text: '市容环境类', value: '市容环境类' },
        { text: '街面秩序类', value: '街面秩序类' },
        { text: '公共设施类', value: '公共设施类' },
        { text: '突发事件类', value: '突发事件类' },
        { text: '牛皮癣', value: '牛皮癣' },
        { text: '其他', value: '其他' },
        { text: '全部', value: '' }
      ],
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        eventdesc: '',
        keyWord: '',
        phone: ''
      }
    }
  },
  computed: {

  },
  mounted() {
    this.initParams();
    this.getRecordList();
  },
  methods: {
    initParams() {
      // 设置当前用户ID
      this.queryParams.phone = this.user.phonenumber || '';
    },

    getStatusText(status) {
      const statusMap = {
        1:'上报',
        2:'立案',
        3:'派遣',
        4:'处置',
        5:'核查',
        6:'结案'
      }
      return statusMap[status] || '';
    },

    // 获取记录列表
    async getRecordList() {
      try {
        // 如果已经在加载中，防止重复请求
        if (this.isLoading) return;

        this.isLoading = true;

        // 显示加载提示
        this.$toast.loading({
          message: '加载中...',
          forbidClick: true,
          duration: 0
        });

        // 设置筛选条件
        this.queryParams.eventdesc = this.filterType;

        // 调用API获取列表
        const res = await getNewsList(this.queryParams);

        // 处理返回数据
        if (res.code === 200 && res.rows && res.rows[0]?.eventList) {
          if (this.queryParams.pageNum === 1) {
            // 第一页，直接设置列表（重置列表）
            this.list = res.rows[0].eventList.map(item => ({
              id: item.id,
              type: this.getEventListText(item.eventdesc).title || '市容环境类',
              description: this.getEventListText(item.eventdesc).desc || '无描述',
              address: item.address || '无地址信息',
              status: item.status || 0
            }));
          } else {
            // 加载更多，追加到列表
            const newItems = res.rows[0].eventList.map(item => ({
              id: item.id,
              type: this.getEventListText(item.eventdesc).title || '市容环境类',
              description: this.getEventListText(item.eventdesc).desc || '无描述',
              address: item.address || '无地址信息',
              status: item.status || 0
            }));
            this.list = [...this.list, ...newItems];
          }

          // 获取总数据量
          this.total = res.rows[0]?.eventList?.length || 0;

          // 判断是否已加载全部数据
          // 如果当前列表长度已经等于或大于总条数，或者API返回的数据为空，则标记为已完成
          const currentPageSize = res.rows[0]?.eventList?.length || 0;
          this.finished = currentPageSize < this.queryParams.pageSize || currentPageSize === 0;
        } else {
          // 如果没有数据，设置列表为空并标记为已完成
          if (this.queryParams.pageNum === 1) {
            this.list = [];
          }
          this.total = 0;
          this.finished = true;
        }

        this.$toast.clear();
        this.isLoading = false;
        this.refreshing = false;
        this.loading = false;
      } catch (error) {
        this.$toast.clear();
        this.isLoading = false;
        this.refreshing = false;
        this.loading = false;
        this.finished = true;
      }
    },

    // 下拉刷新
    onRefresh() {
      // 重置为第一页
      this.queryParams.pageNum = 1;
      // 重置完成状态
      this.finished = false;
      // 获取数据（第一页数据会替换原有列表）
      this.getRecordList();
    },

    // 上拉加载更多
    onLoadMore() {
      if (this.isLoading) return; // 防止重复加载
      this.queryParams.pageNum += 1;
      this.getRecordList();
    },

    getEventListText(str) {
      return {title: str.split(":")[0],desc: str.split(":")[1]}
    },

    // 搜索
    onSearch() {
      // 重置为第一页
      this.queryParams.pageNum = 1;
      // 重置完成状态
      this.finished = false;
      // 重置加载状态，避免触发重复加载
      this.loading = false;
      this.getRecordList();
    },

    // 查看详情
    viewDetail(item) {
      this.$router.push({
        path: '/newsDetail',
        query: { id: item.id }
      });
    },

    // 新增
    onAddNew() {
      this.$router.push('/BrokeTheNews');
    },

    // 返回上一页
    onClickLeft() {
      this.$router.go(-1);
    }
  },
  watch: {
    filterType() {
      // 重置为第一页
      this.queryParams.pageNum = 1;
      // 重置完成状态
      this.finished = false;
      // 重置加载状态，避免触发重复加载
      this.loading = false;
      this.getRecordList();
    }
  }
}
</script>

<style lang="scss" scoped>
.news-history {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 46px;
  padding-bottom: 20px;
}

// 导航栏右侧按钮
.nav-right {
  .add-btn {
    height: 28px;
    font-size: 12px;
    padding: 0 10px;
    color: #1989fa;
  }
}

::v-deep .van-nav-bar {
  background-color: #fff;

  .van-nav-bar__title {
    color: #333;
    font-size: 16px;
    font-weight: 500;
  }

  .van-icon-arrow-left {
    color: #333;
  }
}

// 搜索和筛选区域
.search-filter {
  display: flex;
  padding: 8px;
  background-color: #fff;
  position: sticky;
  top: 46px;
  z-index: 10;
  align-items: center;

  :deep(.van-dropdown-menu__bar) {
    box-shadow: unset;
  }

  .van-dropdown-menu {
    flex: 0 0 auto;
    margin-right: 10px;
  }

  .van-search {
    flex: 1;
    padding: 0 0 0 8px;
  }
}

// 记录列表
.record-list {
  padding: 10px;
}

// 记录项
.record-item {
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

// 记录头部
.record-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .record-type {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }

  .record-status {
    font-size: 14px;

    &.pending {
      color: #1989fa;
    }

    &.processing {
      color: #ff976a;
    }

    &.completed {
      color: #07c160;
    }
  }
}

// 记录内容
.record-content {
  font-size: 13px;
  color: #666;
  line-height: 1.5;

  .record-desc, .record-address {
    margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

// 空状态
.empty-state {
  padding: 40px 0;
}
</style>
