// 浙里办APP适老化设计规范配置文件
export const elderConfig = {
  // 字体大小配置（符合浙里办规范）
  fontSize: {
    base: 20,        // 基础字体大小（最小18px，推荐20px）
    title: 22,       // 标题字体大小
    button: 20,      // 按钮字体大小
    nav: 22,         // 导航字体大小
    large: 24,       // 大字体
    small: 18,       // 小字体（最小值）
    lineHeight: 1.8, // 行高
    multiplier: 1.5  // 字体放大倍数
  },

  // 颜色配置（浙里办高对比度配色方案）
  colors: {
    primary: '#0052D9',      // 浙里办主蓝色
    text: '#000000',         // 纯黑文字
    background: '#ffffff',   // 纯白背景
    border: '#000000',       // 黑色边框
    button: {
      background: '#0052D9',
      text: '#ffffff'
    },
    link: '#0052D9',
    success: '#00A870',      // 成功绿色
    warning: '#FF8800',      // 警告橙色
    error: '#E34D59',        // 错误红色
    disabled: '#CCCCCC',     // 禁用灰色
    secondary: '#333333'     // 次要文字
  },
  
  // 尺寸配置（符合浙里办触摸目标规范）
  sizes: {
    buttonHeight: 56,        // 按钮最小高度（最小44px，推荐56px）
    buttonMinWidth: 88,      // 按钮最小宽度
    navHeight: 64,           // 导航栏高度
    cellHeight: 64,          // 列表项最小高度
    inputHeight: 56,         // 输入框高度
    iconSize: 28,            // 图标大小（最小24px）
    touchTarget: 44,         // 最小触摸目标（44px）
    borderWidth: 2,          // 边框宽度
    focusBorderWidth: 3,     // 焦点边框宽度
    borderRadius: 4,         // 圆角大小（浙里办推荐4px）
    cardRadius: 8,           // 卡片圆角
    padding: 16,             // 内边距
    largePadding: 20,        // 大内边距
    margin: 16,              // 外边距
    largeMargin: 20,         // 大外边距
    spacing: 24              // 元素间距
  },
  
  // 动画配置
  animation: {
    duration: 0.01,          // 动画持续时间（毫秒）
    disabled: true           // 是否禁用动画
  },
  
  // 焦点配置
  focus: {
    outlineWidth: 3,         // 焦点轮廓宽度
    outlineColor: '#1976d2', // 焦点轮廓颜色
    outlineOffset: 2,        // 焦点轮廓偏移
    shadowBlur: 6,           // 焦点阴影模糊度
    shadowColor: 'rgba(25, 118, 210, 0.3)' // 焦点阴影颜色
  },
  
  // 浙里办适老化设计规范要求
  zlbRequirements: {
    // 字体要求
    minFontSize: 18,         // 最小字体大小（强制要求）
    recommendFontSize: 20,   // 推荐字体大小
    maxFontSize: 24,         // 最大字体大小
    minLineHeight: 1.6,      // 最小行高
    recommendLineHeight: 1.8, // 推荐行高

    // 对比度要求（WCAG AA级标准）
    minContrast: 4.5,        // 最小对比度比例
    recommendContrast: 7,    // 推荐对比度比例（AAA级）

    // 触摸目标要求
    minTouchTarget: 44,      // 最小触摸目标大小（px）
    recommendTouchTarget: 56, // 推荐触摸目标大小

    // 间距要求
    minSpacing: 8,           // 最小间距
    recommendSpacing: 16,    // 推荐间距

    // 颜色要求
    highContrast: true,      // 启用高对比度
    colorBlindFriendly: true, // 色盲友好

    // 动画要求
    reduceMotion: true,      // 减少动画
    maxAnimationDuration: 200, // 最大动画时长（毫秒）

    // 无障碍要求
    accessibility: {
      focusVisible: true,    // 焦点可见
      focusOutlineWidth: 4,  // 焦点轮廓宽度
      keyboardNavigation: true, // 键盘导航
      screenReader: true,    // 屏幕阅读器支持
      ariaLabels: true,      // ARIA标签支持
      semanticHTML: true     // 语义化HTML
    },

    // 浙里办特定要求
    zlbSpecific: {
      brandColor: '#0052D9',  // 浙里办品牌色
      borderRadius: 4,        // 统一圆角规范
      shadowDepth: 'subtle',  // 阴影深度
      iconStyle: 'outlined'   // 图标风格
    }
  }
}

// 获取适老化样式
export function getElderStyles() {
  const config = elderConfig
  
  return {
    // CSS变量
    '--elder-font-size': `${config.fontSize.base}px`,
    '--elder-title-size': `${config.fontSize.title}px`,
    '--elder-button-size': `${config.fontSize.button}px`,
    '--elder-nav-size': `${config.fontSize.nav}px`,
    
    '--elder-primary-color': config.colors.primary,
    '--elder-text-color': config.colors.text,
    '--elder-bg-color': config.colors.background,
    '--elder-border-color': config.colors.border,
    '--elder-button-bg': config.colors.button.background,
    '--elder-button-text': config.colors.button.text,
    '--elder-link-color': config.colors.link,
    '--elder-success-color': config.colors.success,
    '--elder-warning-color': config.colors.warning,
    '--elder-error-color': config.colors.error,
    
    '--elder-button-height': `${config.sizes.buttonHeight}px`,
    '--elder-nav-height': `${config.sizes.navHeight}px`,
    '--elder-cell-height': `${config.sizes.cellHeight}px`,
    '--elder-icon-size': `${config.sizes.iconSize}px`,
    '--elder-border-width': `${config.sizes.borderWidth}px`,
    '--elder-border-radius': `${config.sizes.borderRadius}px`,
    '--elder-padding': `${config.sizes.padding}px`,
    '--elder-margin': `${config.sizes.margin}px`,
    
    '--elder-focus-outline-width': `${config.focus.outlineWidth}px`,
    '--elder-focus-outline-color': config.focus.outlineColor,
    '--elder-focus-outline-offset': `${config.focus.outlineOffset}px`,
    '--elder-focus-shadow-blur': `${config.focus.shadowBlur}px`,
    '--elder-focus-shadow-color': config.focus.shadowColor
  }
}

// 检查是否符合浙里办适老化要求
export function validateElderRequirements() {
  const config = elderConfig
  const requirements = config.zlbRequirements
  const errors = []
  
  // 检查字体大小
  if (config.fontSize.base < requirements.minFontSize) {
    errors.push(`基础字体大小 ${config.fontSize.base}px 小于要求的 ${requirements.minFontSize}px`)
  }
  
  // 检查触摸目标大小
  if (config.sizes.buttonHeight < requirements.minTouchTarget) {
    errors.push(`按钮高度 ${config.sizes.buttonHeight}px 小于要求的 ${requirements.minTouchTarget}px`)
  }
  
  // 检查间距
  if (config.sizes.padding < requirements.minSpacing) {
    errors.push(`内边距 ${config.sizes.padding}px 小于要求的 ${requirements.minSpacing}px`)
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

export default elderConfig
