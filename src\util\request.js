import axios from 'axios'
import { mgopRequest } from '@/api/mgopServe'
import request from "@/util/invokeRequest";
import {isInZLBEnvironment} from "@/util/index";

// 浙里办mgop请求封装
async function createMgopRequest(config) {
  try {
    // 提取URL路径，移除baseURL部分
    let apiPath = config.url
    if (config.url.startsWith(process.env.VUE_APP_BASE_API)) {
      apiPath = config.url.replace(process.env.VUE_APP_BASE_API, '')
    }

    // 移除开头的斜杠
    if (apiPath.startsWith('/')) {
      apiPath = apiPath.substring(1)
    }

    // 构建mgop请求参数
    const mgopParams = {
      url: apiPath,
      method: config.method?.toUpperCase() || 'GET',
      data: config.data || config.params || {}
    }

    console.log('使用mgopRequest请求:', mgopParams)

    // 调用mgopRequest（现在是async函数）
    const response = await mgopRequest(mgopParams)
    console.log('mgopRequest响应:', response)

    return response
  } catch (error) {
    console.error('mgopRequest请求失败:', error)

    // 统一错误格式
    const errorResponse = {
      data: {
        code: 500,
        msg: error || '请求失败'
      },
      status: 500,
      statusText: 'Internal Server Error',
      config: config
    }

    throw errorResponse
  }
}

axios.defaults.withCredentials = true

// 智能请求服务 - 根据环境自动选择请求方式
const smartRequest = (config) => {
  //示例
  // config = {
  //   url: '/xzzfj/xzzfjZfsb/getArea',
  //   method: 'get',
  // }

  let newConfig = setinvokeConfig(config)

  // 检查是否在浙里办环境
  if (isInZLBEnvironment()) {
    console.log('检测到浙里办环境，使用mgopRequest')
    return createMgopRequest(newConfig)
  } else {
    console.log('使用默认axios请求')
    return request(newConfig)
  }
}

const setinvokeConfig = (config) => {
  if (config.method == 'get') {
    return {
      url: '/invoke/call',
      method: 'post',
      data: {
        url: config.url,
        method: config.method,
        params: config.params || {}
      },
    }
  } else if (config.method == 'post' || config.method == 'put') {
    return {
      url: '/invoke/call',
      method: 'post',
      data: {
        url: config.url,
        method: config.method,
        body: config.data || {}
      },
    }
  } else {
    return {
      url: '/invoke/call',
      method: 'post',
      data: {
        url: config.url,
        method: config.method
      },
    }
  }
}

// 添加常用的HTTP方法，保持与axios API兼容
smartRequest.get = (url, config = {}) => {
  return smartRequest({ ...config, method: 'GET', url })
}

smartRequest.post = (url, data, config = {}) => {
  return smartRequest({ ...config, method: 'POST', url, data })
}

smartRequest.put = (url, data, config = {}) => {
  return smartRequest({ ...config, method: 'PUT', url, data })
}

smartRequest.delete = (url, config = {}) => {
  return smartRequest({ ...config, method: 'DELETE', url })
}

smartRequest.patch = (url, data, config = {}) => {
  return smartRequest({ ...config, method: 'PATCH', url, data })
}

// 添加环境检测方法
smartRequest.isZLBEnvironment = isInZLBEnvironment()

// 添加强制使用特定请求方式的方法
smartRequest.forceAxios = (config) => {
  console.log('强制使用axios请求')
  let newConfig = setinvokeConfig(config)
  return request(newConfig)
}

smartRequest.forceMgop = (config) => {
  console.log('强制使用mgop请求')
  return createMgopRequest(config)
}

export default smartRequest
