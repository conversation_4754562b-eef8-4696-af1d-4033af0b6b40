<template>
  <div class="news-detail" :class="elderModeClass">
    <!-- 导航栏 -->
    <van-nav-bar title="详情" fixed>
      <template #left>
        <div class="nav-right">
          <span class="save-draft" @click="historyPage">上报记录</span>
        </div>
      </template>
    </van-nav-bar>

    <!-- 详情内容 -->
    <div class="detail-content" v-if="detailData">
      <!-- 基本信息 -->
      <div class="info-section">
        <div class="info-row">
          <div class="info-label">问题分类：</div>
          <div class="info-value">
            {{ getEventListText(detailData.eventdesc).title }}
          </div>
        </div>

        <div class="info-row">
          <div class="info-label">归属区县：</div>
          <div class="info-value">
            {{ getDictText(areaOptions, detailData.areaid) }}
          </div>
        </div>

        <div class="info-row">
          <div class="info-label">归属街道：</div>
          <div class="info-value">{{ detailData.streetid }}</div>
        </div>

        <div class="info-row">
          <div class="info-label">详细地址：</div>
          <div class="info-value">{{ detailData.address }}</div>
        </div>

        <div class="info-row">
          <div class="info-label">问题描述：</div>
          <div class="info-value">
            {{ getEventListText(detailData.eventdesc).desc }}
          </div>
        </div>
      </div>

      <!-- 现场照片 -->
      <div class="photo-section">
        <div class="section-title">现场照片：</div>
        <div class="photo-list">
          <vantFileUpload
            disabled
            v-model="detailData.fileStr"
            :file-type="['jpg', 'png']"
          />
        </div>
      </div>

      <!-- 结果反馈 -->
      <div class="feedback-section" v-if="detailData.feedback">
        <div class="section-title">结果反馈：</div>
        <div class="feedback-content">{{ detailData.feedback }}</div>
      </div>
    </div>

    <!-- 加载中 -->
    <div class="loading-container" v-if="isLoading">
      <van-loading type="spinner" color="#1989fa" />
    </div>
  </div>
</template>

<script>
import { getProblemDetail } from "@/api/common";
import vantFileUpload from "@/components/vant-file-upload";
import elderMode from "@/mixins/elderMode";

export default {
  name: "newsDetail",
  mixins: [elderMode],
  components: {
    vantFileUpload,
  },
  data() {
    return {
      isLoading: false,
      detailData: null,
      photoList: [],
      areaOptions: [],
    };
  },
  mounted() {
    this.getDictList();
    this.getDetail();
  },
  methods: {
    historyPage() {
      this.$router.push("/wxNewsHistory");
    },
    getDictList() {
      //区划
      this.getDicts("county").then((response) => {
        this.areaOptions = response.data.map((item) => ({
          label: item.dictLabel,
          value: item.dictValue,
        }));
      });
    },
    // 获取详情
    async getDetail() {
      try {
        this.isLoading = true;

        // 显示加载提示
        this.$toast.loading({
          message: "加载中...",
          forbidClick: true,
          duration: 0,
        });

        // 获取详情数据
        const res = await getProblemDetail(this.$route.query.id);

        if (res.code === 200 && res.data) {
          this.detailData = res.data;
        } else {
          this.$toast.fail("获取详情失败");
        }

        this.$toast.clear();
        this.isLoading = false;
      } catch (error) {
        console.error("获取详情失败:", error);
        this.$toast.clear();
        this.$toast.fail("获取详情失败");
        this.isLoading = false;
      }
    },

    getDictText(arr, value) {
      return arr.find((item) => item.value === value).label || "暂无数据";
    },

    getEventListText(str) {
      return { title: str.split(":")[0], desc: str.split(":")[1] };
    },

    // 预览图片
    previewImage(fileList, index) {
      const images = fileList.map((item) => item.url);
      this.$imagePreview({
        images,
        startPosition: index,
        closeable: true,
      });
    },

    // 返回上一页
    onClickLeft() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.news-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 46px;
  padding-bottom: 20px;
}

// 详情内容
.detail-content {
  padding: 15px;
}

// 信息区域
.info-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

// 信息行
.info-row {
  display: flex;
  margin-bottom: 12px;
  line-height: 1.5;

  &:last-child {
    margin-bottom: 0;
  }

  .info-label {
    flex: 0 0 auto;
    width: 90px;
    color: #666;
    font-size: 14px;
  }

  .info-value {
    flex: 1;
    color: #333;
    font-size: 14px;
    word-break: break-all;
  }
}

// 照片区域
.photo-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

// 区域标题
.section-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

// 照片列表
.photo-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

// 照片项
.photo-item {
  width: calc(50% - 10px);
  height: 120px;
  margin: 0 5px 10px;
  border-radius: 4px;
  overflow: hidden;
}

// 反馈区域
.feedback-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
}

// 反馈内容
.feedback-content {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}

// 加载容器
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}
</style>
