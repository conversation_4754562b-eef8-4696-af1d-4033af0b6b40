// 适老化工具函数
import { elderConfig, getElderStyles } from '@/config/elderConfig'

/**
 * 应用适老化样式到DOM元素
 * @param {HTMLElement} element - 目标元素
 */
export function applyElderStyles(element) {
  if (!element) return

  const styles = getElderStyles()

  // 应用CSS变量
  Object.keys(styles).forEach(key => {
    element.style.setProperty(key, styles[key])
  })

  // 添加适老化类名
  element.classList.add('elder-mode')
}

/**
 * 移除适老化样式
 * @param {HTMLElement} element - 目标元素
 */
export function removeElderStyles(element) {
  if (!element) return

  const styles = getElderStyles()

  // 移除CSS变量
  Object.keys(styles).forEach(key => {
    element.style.removeProperty(key)
  })

  // 移除适老化类名
  element.classList.remove('elder-mode')
}

/**
 * 检测是否在浙里办环境中
 * @returns {boolean}
 */
export function isInZLBEnvironment() {
  const userAgent = navigator.userAgent
  return /ZhejiangZWFW/.test(userAgent) ||
    /AlipayClient/.test(userAgent) ||
    /ZWF/.test(userAgent) ||
    /ZLB/.test(userAgent)
}

/**
 * 获取浙里办UI样式
 * @returns {Promise<string>}
 */
export function getZLBUIStyle() {
  return new Promise((resolve) => {
    if (!window.ZWJSBridge || !window.ZWJSBridge.getUiStyle) {
      resolve('normal')
      return
    }

    window.ZWJSBridge.getUiStyle({})
      .then(result => {
        resolve(result?.uiStyle || 'normal')
      })
      .catch(() => {
        resolve('normal')
      })
  })
}

/**
 * 设置字体大小
 * @param {number} size - 字体大小
 */
export function setFontSize(size) {
  const html = document.documentElement
  html.style.fontSize = `${size}px`

  // 触发rem重新计算
  if (window.setRem) {
    window.setRem()
  }
}

/**
 * 增强焦点样式
 */
export function enhanceFocusStyles() {
  const style = document.createElement('style')
  style.id = 'elder-focus-enhancement'
  style.textContent = `
    .elder-mode *:focus {
      outline: var(--elder-focus-outline-width) solid var(--elder-focus-outline-color) !important;
      outline-offset: var(--elder-focus-outline-offset) !important;
      box-shadow: 0 0 0 var(--elder-focus-shadow-blur) var(--elder-focus-shadow-color) !important;
    }
    .elder-mode button:focus,
    .elder-mode .van-button:focus {
      transform: scale(1.05) !important;
    }
  `

  if (!document.getElementById('elder-focus-enhancement')) {
    document.head.appendChild(style)
  }
}

/**
 * 禁用动画
 */
export function disableAnimations() {
  const style = document.createElement('style')
  style.id = 'elder-no-animations'
  style.textContent = `
    .elder-mode *,
    .elder-mode *::before,
    .elder-mode *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      transition-delay: 0ms !important;
    }
  `

  if (!document.getElementById('elder-no-animations')) {
    document.head.appendChild(style)
  }
}

/**
 * 设置高对比度
 */
export function setHighContrast() {
  const style = document.createElement('style')
  style.id = 'elder-high-contrast'
  style.textContent = `
    .elder-mode {
      filter: contrast(1.2) !important;
    }
    .elder-mode * {
      text-shadow: none !important;
      box-shadow: none !important;
    }
  `

  if (!document.getElementById('elder-high-contrast')) {
    document.head.appendChild(style)
  }
}

/**
 * 获取可聚焦元素
 * @param {HTMLElement} container - 容器元素
 * @returns {HTMLElement[]}
 */
export function getFocusableElements(container = document) {
  const selector = [
    'button',
    '[href]',
    'input',
    'select',
    'textarea',
    '[tabindex]:not([tabindex="-1"])',
    '.van-button',
    '.van-cell--clickable'
  ].join(',')

  return Array.from(container.querySelectorAll(selector))
    .filter(el => !el.disabled && el.offsetParent !== null)
}

/**
 * 键盘导航处理
 * @param {KeyboardEvent} event - 键盘事件
 */
export function handleKeyboardNavigation(event) {
  // Tab键导航增强
  if (event.key === 'Tab') {
    const focusableElements = getFocusableElements()
    const currentIndex = focusableElements.indexOf(document.activeElement)

    if (event.shiftKey) {
      // Shift+Tab 向前导航
      const prevIndex = currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1
      focusableElements[prevIndex]?.focus()
      event.preventDefault()
    } else {
      // Tab 向后导航
      const nextIndex = currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0
      focusableElements[nextIndex]?.focus()
      event.preventDefault()
    }
  }

  // Enter键激活
  if (event.key === 'Enter' && document.activeElement) {
    const element = document.activeElement
    if (element.tagName === 'BUTTON' || element.classList.contains('van-button')) {
      element.click()
    }
  }
}

/**
 * 清理适老化样式
 */
export function cleanupElderStyles() {
  const stylesToRemove = [
    'elder-high-contrast',
    'elder-no-animations',
    'elder-focus-enhancement'
  ]

  stylesToRemove.forEach(id => {
    const style = document.getElementById(id)
    if (style) {
      style.remove()
    }
  })
}

/**
 * 强制重置所有样式到默认状态
 */
export function forceResetStyles() {
  const html = document.documentElement
  const body = document.body

  console.log('强制重置样式到默认状态...')

  // 移除所有适老化类名
  body.classList.remove('elder-mode')
  html.classList.remove('elder-mode')

  // 清理所有适老化样式表
  cleanupElderStyles()

  // 移除所有CSS变量
  const allCSSVariables = [
    '--elder-font-size-base',
    '--elder-font-size-large',
    '--elder-font-size-small',
    '--elder-line-height',
    '--elder-primary-color',
    '--elder-text-color',
    '--elder-bg-color',
    '--elder-border-color',
    '--elder-button-bg',
    '--elder-button-text',
    '--elder-link-color',
    '--elder-success-color',
    '--elder-warning-color',
    '--elder-error-color',
    '--elder-disabled-color',
    '--elder-secondary-text',
    '--elder-contrast-ratio'
  ]

  allCSSVariables.forEach(variable => {
    html.style.removeProperty(variable)
    body.style.removeProperty(variable)
  })

  // 重置字体大小
  html.style.removeProperty('font-size')
  body.style.removeProperty('font-size')

  // 重置滤镜
  html.style.removeProperty('filter')
  body.style.removeProperty('filter')

  // 移除所有内联样式（如果包含elder相关）
  if (html.getAttribute('style')) {
    const htmlStyle = html.getAttribute('style')
    const cleanHtmlStyle = htmlStyle.replace(/[^;]*elder[^;]*;?/g, '').trim()
    if (cleanHtmlStyle) {
      html.setAttribute('style', cleanHtmlStyle)
    } else {
      html.removeAttribute('style')
    }
  }

  if (body.getAttribute('style')) {
    const bodyStyle = body.getAttribute('style')
    const cleanBodyStyle = bodyStyle.replace(/[^;]*elder[^;]*;?/g, '').trim()
    if (cleanBodyStyle) {
      body.setAttribute('style', cleanBodyStyle)
    } else {
      body.removeAttribute('style')
    }
  }

  // 重新设置rem基准值
  if (window.setRem) {
    window.setRem()
  }

  console.log('样式重置完成')
}

/**
 * 初始化适老化模式
 * @param {boolean} isElderMode - 是否为适老化模式
 */
export function initElderMode(isElderMode) {
  const body = document.body
  const html = document.documentElement

  if (isElderMode) {
    // 应用适老化样式
    applyElderStyles(html)
    applyElderStyles(body)

    // 设置字体大小
    setFontSize(elderConfig.fontSize.base)

    // 设置高对比度
    setHighContrast()

    // 禁用动画
    disableAnimations()

    // 增强焦点样式
    enhanceFocusStyles()

  } else {
    // 移除适老化样式
    removeElderStyles(html)
    removeElderStyles(body)

    // 清理样式
    cleanupElderStyles()

    // 恢复默认字体大小
    if (window.setRem) {
      window.setRem()
    }
  }
}

/**
 * 检查颜色对比度
 * @param {string} foreground - 前景色
 * @param {string} background - 背景色
 * @returns {number} 对比度比例
 */
export function checkColorContrast(foreground, background) {
  // 简化的对比度计算，实际项目中可能需要更精确的算法
  const getLuminance = (color) => {
    // 这里应该实现真正的亮度计算
    // 为了简化，返回一个模拟值
    return 0.5
  }

  const l1 = getLuminance(foreground)
  const l2 = getLuminance(background)

  const lighter = Math.max(l1, l2)
  const darker = Math.min(l1, l2)

  return (lighter + 0.05) / (darker + 0.05)
}

export default {
  applyElderStyles,
  removeElderStyles,
  isInZLBEnvironment,
  getZLBUIStyle,
  setFontSize,
  enhanceFocusStyles,
  disableAnimations,
  setHighContrast,
  getFocusableElements,
  handleKeyboardNavigation,
  cleanupElderStyles,
  initElderMode,
  checkColorContrast
}
