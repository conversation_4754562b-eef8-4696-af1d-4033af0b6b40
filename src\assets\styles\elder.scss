/* 浙里办APP适老化设计规范样式 */

// 适老化模式下的全局样式
.elder-mode {
  // 字体大小规范：最小18px，推荐20px，行高1.8
  font-size: 20px !important;
  line-height: 1.8 !important;

  // 浙里办适老化颜色规范 - 高对比度配色方案
  --elder-primary-color: #0052D9;      // 浙里办主蓝色
  --elder-text-color: #333333;         // 纯黑文字，确保最高对比度
  --elder-bg-color: #ffffff;           // 纯白背景
  --elder-border-color: #000000;       // 黑色边框，增强对比度
  --elder-button-bg: #0052D9;          // 按钮背景色
  --elder-button-text: #ffffff;        // 按钮文字白色
  --elder-link-color: #0052D9;         // 链接颜色
  --elder-success-color: #00A870;      // 成功状态绿色
  --elder-warning-color: #FF8800;      // 警告状态橙色
  --elder-error-color: #E34D59;        // 错误状态红色
  --elder-disabled-color: #CCCCCC;     // 禁用状态灰色
  --elder-secondary-text: #333333;     // 次要文字颜色

  // 全局文字样式
  * {
    font-size: inherit !important;
    line-height: inherit !important;
  }

  // 标题样式
  h1, h2, h3, h4, h5, h6 {
    font-weight: bold !important;
    color: var(--elder-text-color) !important;
    margin-bottom: 16px !important;
  }

  h1 { font-size: 28px !important; }
  h2 { font-size: 24px !important; }
  h3 { font-size: 22px !important; }
  h4 { font-size: 20px !important; }
  h5 { font-size: 18px !important; }
  h6 { font-size: 16px !important; }

  // 按钮样式规范：最小高度56px，字体20px，圆角4px
  .van-button, button {
    min-height: 56px !important;
    min-width: 88px !important;
    font-size: 20px !important;
    font-weight: 600 !important;
    border-radius: 4px !important;
    padding: 16px 24px !important;
    letter-spacing: 0.5px !important;

    &.van-button--primary {
      background-color: #e8f3ff !important;
      color: #1989fa !important;
      //border-color: var(--elder-button-bg) !important;
      //box-shadow: 0 2px 4px rgba(0, 82, 217, 0.3) !important;
    }

    &.van-button--default {
      background-color: var(--elder-bg-color) !important;
      color: var(--elder-text-color) !important;
      border-color: var(--elder-border-color) !important;
    }

    &.van-button--success {
      background-color: var(--elder-success-color) !important;
      border-color: var(--elder-success-color) !important;
    }

    &.van-button--warning {
      background-color: var(--elder-warning-color) !important;
      border-color: var(--elder-warning-color) !important;
    }

    &.van-button--danger {
      background-color: var(--elder-error-color) !important;
      border-color: var(--elder-error-color) !important;
    }

    &:disabled {
      background-color: var(--elder-disabled-color) !important;
      color: #999999 !important;
      border-color: var(--elder-disabled-color) !important;
    }
  }

  // 导航栏样式规范：高度64px，标题22px
  .van-nav-bar {
    height: 64px !important;
    background-color: var(--elder-bg-color) !important;
    padding: 0 16px !important;

    .van-nav-bar__title {
      font-size: 22px !important;
      font-weight: 600 !important;
      color: var(--elder-text-color) !important;
      letter-spacing: 0.5px !important;
    }

    .van-nav-bar__left, .van-nav-bar__right {
      .van-icon {
        font-size: 28px !important;
        color: var(--elder-text-color) !important;
        min-width: 44px !important;
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      .van-nav-bar__text {
        font-size: 18px !important;
        font-weight: 500 !important;
        color: var(--elder-primary-color) !important;
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
        padding: 0 8px !important;
      }
    }
  }

  // 输入框样式规范：最小高度56px，字体20px
  .van-field {
    min-height: 56px !important;
    font-size: 20px !important;
    padding: 16px !important;
    //border: 2px solid var(--elder-border-color) !important;
    border-radius: 4px !important;
    background-color: var(--elder-bg-color) !important;

    .van-field__label {
      font-size: 20px !important;
      font-weight: 600 !important;
      color: var(--elder-text-color) !important;
      min-width: 120px !important;
      margin-right: 16px !important;
    }

    .van-field__control {
      font-size: 20px !important;
      color: var(--elder-text-color) !important;
      line-height: 1.6 !important;
      padding: 0 !important;

      &::placeholder {
        color: #999999 !important;
        font-size: 18px !important;
      }
    }

    .van-field__error-message {
      font-size: 16px !important;
      color: var(--elder-error-color) !important;
      margin-top: 8px !important;
    }
  }

  // 列表样式规范：最小高度64px，字体20px
  .van-cell {
    min-height: 64px !important;
    font-size: 20px !important;
    padding: 16px 16px 0 16px !important;
    border-bottom: 2px solid #E5E5E5 !important;
    background-color: var(--elder-bg-color) !important;

    .van-cell__title {
      font-size: 20px !important;
      font-weight: 500 !important;
      color: var(--elder-text-color) !important;
      line-height: 1.6 !important;
    }

    .van-cell__value {
      font-size: 20px !important;
      color: var(--elder-secondary-text) !important;
      line-height: 1.6 !important;
    }

    .van-cell__label {
      font-size: 16px !important;
      color: #666666 !important;
      margin-top: 4px !important;
    }

    .van-cell__right-icon {
      font-size: 24px !important;
      color: var(--elder-text-color) !important;
      margin-left: 16px !important;
    }

    &.van-cell--clickable:active {
      background-color: #F5F5F5 !important;
    }
  }

  // 搜索框样式规范：高度56px，字体20px
  .van-search {
    //padding: 16px !important;
    background-color: var(--elder-bg-color) !important;

    .van-search__content {
      background-color: var(--elder-bg-color) !important;
      //border: 2px solid var(--elder-border-color) !important;
      border-radius: 4px !important;
      //height: 56px !important;
    }

    .van-field__control {
      font-size: 20px !important;
      color: var(--elder-text-color) !important;
      line-height: 1.6 !important;

      &::placeholder {
        color: #999999 !important;
        font-size: 18px !important;
      }
    }

    .van-search__action {
      font-size: 18px !important;
      color: var(--elder-primary-color) !important;
      font-weight: 500 !important;
      margin-left: 16px !important;
    }
  }

  // 卡片样式规范：圆角8px，内边距20px
  .van-card, .card-item, .record-item, .module-item {
    border: 2px solid #E5E5E5 !important;
    border-radius: 8px !important;
    background-color: var(--elder-bg-color) !important;
    margin-bottom: 20px !important;
    padding: 20px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;

    .van-card__title, .card-title, .record-type {
      font-size: 22px !important;
      font-weight: 600 !important;
      color: var(--elder-text-color) !important;
      line-height: 1.6 !important;
      margin-bottom: 12px !important;
    }

    .van-card__desc, .card-desc, .record-desc, .record-address {
      font-size: 20px !important;
      color: var(--elder-secondary-text) !important;
      line-height: 1.8 !important;
      margin-bottom: 8px !important;
    }

    .van-card__price {
      font-size: 20px !important;
      color: var(--elder-primary-color) !important;
      font-weight: 600 !important;
    }
  }

  // 图标样式
  .van-icon {
    font-size: 24px !important;
  }

  // 链接样式
  a {
    color: var(--elder-link-color) !important;
    font-size: 18px !important;
    text-decoration: underline !important;
  }

  // 状态颜色
  .success, .van-button--success {
    background-color: var(--elder-success-color) !important;
    color: var(--elder-button-text) !important;
  }

  .warning, .van-button--warning {
    background-color: var(--elder-warning-color) !important;
    color: var(--elder-button-text) !important;
  }

  .error, .van-button--danger {
    background-color: var(--elder-error-color) !important;
    color: var(--elder-button-text) !important;
  }

  // 模块网格样式规范：图标64px，间距24px
  .module-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)) !important;
    grid-gap: 24px !important;
    padding: 20px !important;

    .module-item {
      padding: 24px 16px !important;
      text-align: center !important;
      min-height: 120px !important;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
      cursor: pointer !important;
      transition: all 0.2s ease !important;

      .module-icon {
        width: 64px !important;
        height: 64px !important;
        margin-bottom: 16px !important;
        display: block !important;
      }

      .module-name {
        font-size: 18px !important;
        font-weight: 600 !important;
        color: var(--elder-text-color) !important;
        line-height: 1.4 !important;
        text-align: center !important;
        word-break: break-all !important;
      }

      &:hover, &:active {
        background-color: #F0F8FF !important;
        border-color: var(--elder-primary-color) !important;
        transform: translateY(-2px) !important;
      }
    }
  }

  // 空状态样式
  .van-empty {
    .van-empty__description {
      font-size: 18px !important;
      color: var(--elder-text-color) !important;
    }
  }

  // Toast样式
  .van-toast {
    font-size: 18px !important;
    min-width: 200px !important;
    padding: 16px !important;
  }

  // 对话框样式
  .van-dialog {
    .van-dialog__header {
      font-size: 20px !important;
      font-weight: bold !important;
    }

    .van-dialog__message {
      font-size: 18px !important;
      line-height: 1.6 !important;
    }

    .van-dialog__footer {
      .van-button {
        font-size: 18px !important;
        min-height: 48px !important;
      }
    }
  }

  // 标签页样式
  .van-tabs {
    .van-tab {
      font-size: 18px !important;
      font-weight: bold !important;
      min-height: 48px !important;
    }

    .van-tabs__line {
      height: 4px !important;
    }
  }

  // 下拉菜单样式
  .van-dropdown-menu {
    .van-dropdown-item {
      font-size: 18px !important;
      min-height: 48px !important;
    }
  }

  // 步骤条样式
  .van-steps {
    .van-step__title {
      font-size: 16px !important;
      font-weight: bold !important;
    }

    .van-step__desc {
      font-size: 14px !important;
    }
  }
}

// 适老化模式下的动画减少规范
.elder-mode * {
  transition: none !important;
  animation: none !important;
}

// 适老化模式下的焦点样式增强规范
.elder-mode *:focus {
  //outline: 4px solid var(--elder-primary-color) !important;
  //outline-offset: 2px !important;
  //box-shadow: 0 0 0 8px rgba(0, 82, 217, 0.2) !important;
  z-index: 999 !important;
}

// 按钮焦点增强
.elder-mode button:focus,
.elder-mode .van-button:focus {
  transform: scale(1.05) !important;
  //box-shadow: 0 0 0 4px var(--elder-primary-color), 0 0 0 8px rgba(0, 82, 217, 0.2) !important;
}

// 链接焦点增强
.elder-mode a:focus {
  background-color: rgba(0, 82, 217, 0.1) !important;
  text-decoration: underline !important;
  text-decoration-thickness: 3px !important;
}

// 输入框焦点增强
.elder-mode input:focus,
.elder-mode textarea:focus,
.elder-mode .van-field__control:focus {
  //border-color: var(--elder-primary-color) !important;
  //border-width: 3px !important;
  //box-shadow: 0 0 0 4px rgba(0, 82, 217, 0.2) !important;
}

// 无障碍辅助样式
.elder-mode {
  // 跳转链接（屏幕阅读器）
  .sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  }

  // 高对比度模式
  @media (prefers-contrast: high) {
    --elder-text-color: #000000;
    --elder-bg-color: #ffffff;
    --elder-border-color: #000000;
  }

  // 减少动画模式
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}
