<!--今日统计-->
<template>
  <div class="today-statistics" :class="elderModeClass">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="今日统计"
      left-arrow
      fixed
      @click-left="onClickLeft"
    />

    <!-- 统计表格 -->
    <div class="statistics-table">
      <div class="table-header">
        <div class="col type-col">类型</div>
        <div class="col count-col">数量</div>
        <div class="col completed-col">完成数</div>
        <div class="col rate-col">完成率</div>
      </div>
      <div class="table-body">
        <div class="table-row" v-for="(item, index) in statisticsList" :key="index">
          <div class="col type-col">{{ item.type }}</div>
          <div class="col count-col">{{ item.count }}</div>
          <div class="col completed-col">{{ item.completed }}</div>
          <div class="col rate-col" :class="getRateClass(item.rate)">{{ item.rate }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getTodayStatistics} from "@/api/common";
import elderMode from "@/mixins/elderMode";

export default {
  name: "TodayStatistics",
  mixins: [elderMode],
  data() {
    return {
      statisticsList: []
    }
  },
  mounted() {
    this.getStatisticsData();
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },

    // 获取统计数据
    getStatisticsData() {
      // 这里可以添加API调用获取实际数据
      getTodayStatistics().then(res => {
        this.statisticsList = res.rows.map(item => ({
          type: item.lx,
          count: item.sl,
          completed: item.wcs,
          rate: item.wcl
        }));
      })
    },

    // 根据完成率返回对应的样式类
    getRateClass(rate) {
      if (rate < 70) {
        return 'rate-low';
      } else if (rate < 90) {
        return 'rate-medium';
      } else {
        return 'rate-high';
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.today-statistics {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 46px;

  .date-display {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f5f5f5;

    .date-label {
      color: #333;
      font-size: 14px;
      margin-right: 8px;
    }

    .date-value {
      flex: 1;
      color: #333;
      font-size: 14px;
    }

    .calendar-icon {
      color: #999;
    }
  }

  .statistics-table {
    margin: 16px 16px 16px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;

    .table-header {
      display: flex;
      padding: 14px 0;
      border-bottom: 1px solid #f2f2f2;

      .col {
        text-align: center;
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        padding: 14px 0;
        border-bottom: 1px solid #f2f2f2;

        &:last-child {
          border-bottom: none;
        }

        .col {
          text-align: center;
          color: #333;
          font-size: 14px;
        }

        .rate-low {
          color: #409eff;
        }

        .rate-medium {
          color: #409eff;
        }

        .rate-high {
          color: #409eff;
        }
      }
    }

    .type-col {
      flex: 2;
      text-align: left !important;
      padding-left: 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .count-col, .completed-col, .rate-col {
      flex: 1;
    }
  }
}
</style>
