<!--
 * @Description:
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-07-02 09:10:10
 * @LastEditors: wjb
 * @LastEditTime: 2025-07-05 14:28:44
-->
<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title><%= htmlWebpackPlugin.options.title %></title>
  <link rel="stylesheet" href="<%= BASE_URL %>./leaflet/leaflet.css" />
  <script src="<%= BASE_URL %>./leaflet/leaflet.js"></script>
  <script type="text/javascript" src="//assets.zjzwfw.gov.cn/assets/ZWJSBridge/1.1.0/zwjsbridge.js"></script>
  <script type="text/javascript" src="//assets.zjzwfw.gov.cn/assets/zwlog/1.0.0/zwlog.js"></script>
  <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"
          onerror="console.error('微信JSSDK加载失败，请检查网络连接')"></script>
  <script>
    // 保存原始的wx对象为jWeixin，避免冲突
    if (window.wx) {
      window.jWeixin = window.wx
      delete window.wx
      console.log('微信JSSDK加载成功，版本:', window.jWeixin.version || 'unknown')
    } else {
      console.warn('微信JSSDK未正确加载')
    }
  </script>
  <script>
    (function (w, d, s, q, i) {
      w[q] = w[q] || [];
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s);
      j.async = true;
      j.id = "beacon-aplus";
      j.src = "https://d.alicdn.com/alilog/mlog/aplus.js?id=202951085";
      f.parentNode.insertBefore(j, f);
    })(window, document, "script", "aplus_queue");
    aplus_queue.push({
      action: "aplus.setMetaInfo",
      arguments: ["aplus-rhost-v", "alog.zjzwfw.gov.cn"]
    });
    aplus_queue.push({
      action: "aplus.setMetaInfo",
      arguments: ["aplus-rhost-g", "alog.zjzwfw.gov.cn"]
    });
    aplus_queue.push({
      action: "aplus.setMetaInfo",
      arguments: ["appId", "60506758"]
    });
  </script>
<!--  <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>-->
<!--  <script>-->
<!--    var vConsole = new window.VConsole()-->
<!--  </script>-->
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
      Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>
