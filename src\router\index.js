import Vue from 'vue'
import VueRouter from 'vue-router'
import { getToken } from '@/util/auth'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    component: () => import('@/views/layout'),
    children: [
      {
        // 首页
        name: 'Home',
        path: '', // 默认子路由
        component: () => import('@/views/home'),
        meta: { title: '首页' },
      },
      {
        // 登录页
        name: 'Login',
        path: '/login',
        component: () => import('@/views/login'),
        meta: { title: '登录' },
      },
      {
        // 个人中心
        name: 'PersonCenter',
        path: '/personCenter',
        component: () => import('@/views/personCenter'),
        meta: { title: '个人中心' },
      }
    ],
  },{
    name: 'IdiomaticExpressions',
    path: '/IdiomaticExpressions',
    component: () => import('@/views/IdiomaticExpressions'),
    meta: { title: '惯用语设置' },
  },{
    name: 'clockIn',
    path: '/clockIn',
    component: () => import('@/views/clockIn'),
    meta: { title: '考勤入口' },
  },
  {
    name: 'CollectorClockIn',
    path: '/CollectorClockIn',
    component: () => import('@/views/clockIn/CollectorClockIn'),
    meta: { title: '采集员考勤' },
  },
  {
    name: 'StationFrontAttendance',
    path: '/StationFrontAttendance',
    component: () => import('@/views/clockIn/StationFrontAttendance'),
    meta: { title: '站前考勤' },
  },
  {
    name: 'CaseFileArchives',
    path: '/CaseFileArchives',
    component: () => import('@/views/CaseFileArchives'),
    meta: { title: '案卷档案' },
  },
  {
    name: 'CompletedTasks',
    path: '/CompletedTasks',
    component: () => import('@/views/CompletedTasks'),
    meta: { title: '已办任务' },
  },
  {
    name: 'staffLocation',
    path: '/staffLocation',
    component: () => import('@/views/staffLocation'),
    meta: { title: '人员定位' },
  },
  {
    name: 'staffLocationDetail',
    path: '/staffLocationDetail',
    component: () => import('@/views/staffLocation/Detail'),
    meta: { title: '人员定位' },
  },
  {
    name: 'TodayStatistics',
    path: '/TodayStatistics',
    component: () => import('@/views/TodayStatistics'),
    meta: { title: '今日统计' },
  },
  {
    name: 'DailyPatrol',
    path: '/DailyPatrol',
    component: () => import('@/views/CaseFileArchives/childDetailPage/DailyPatrol'),
    meta: { title: '日常任务' },
  },
  {
    name: 'FourInOne',
    path: '/FourInOne',
    component: () => import('@/views/CaseFileArchives/childDetailPage/FourInOne'),
    meta: { title: '四位一体' },
  },
  {
    name: 'GeneralCases',
    path: '/GeneralCases',
    component: () => import('@/views/CaseFileArchives/childDetailPage/GeneralCases'),
    meta: { title: '一般案件' },
  },
  {
    name: 'Snap',
    path: '/Snap',
    component: () => import('@/views/CaseFileArchives/childDetailPage/Snap'),
    meta: { title: '抓拍详情' },
  },
  {
    name: 'ViolationDisposal',
    path: '/ViolationDisposal',
    component: () => import('@/views/CaseFileArchives/childDetailPage/ViolationDisposal'),
    meta: { title: '违规处置' },
  },
  {
    name: 'YellowCattleDisposal',
    path: '/YellowCattleDisposal',
    component: () => import('@/views/CaseFileArchives/childDetailPage/YellowCattleDisposal'),
    meta: { title: '黄牛处置' },
  },
  {
    name: 'ToDoTasksDetail',
    path: '/ToDoTasksDetail',
    component: () => import('@/views/CaseFileArchives/childDetailPage/ToDoTasksDetail'),
    meta: { title: '案件详情' },
  },
  {
    name: 'PsoriasisDetail',
    path: '/PsoriasisDetail',
    component: () => import('@/views/CaseFileArchives/childDetailPage/PsoriasisDetail'),
    meta: { title: '牛皮藓详情' },
  },
  {
    name: 'SpecialRectificationSubmit',
    path: '/SpecialRectificationSubmit',
    component: () => import('@/views/CaseFileArchives/childDetailPage/SpecialRectification/Submit'),
    meta: { title: '专项整治' },
  },
  {
    name: 'SpecialRectificationSubmitZz',
    path: '/SpecialRectificationSubmitZz',
    component: () => import('@/views/CaseFileArchives/childDetailPage/SpecialRectification/SubmitZz'),
    meta: { title: '专项整治' },
  },
  {
    name: 'BrokeTheNews',
    path: '/BrokeTheNews',
    component: () => import('@/views/BrokeTheNews/index'),
    meta: { title: '我要爆料' },
  },
  {
    name: 'newsHistory',
    path: '/newsHistory',
    component: () => import('@/views/BrokeTheNews/childPage/newsHistory'),
    meta: { title: '我的上报记录' },
  },
  {
    name: 'newsDetail',
    path: '/newsDetail',
    component: () => import('@/views/BrokeTheNews/childPage/newsDetail'),
    meta: { title: '详情' },
  },
  {
    name: 'wxBrokeTheNews',
    path: '/wxBrokeTheNews',
    component: () => import('@/views/wxPages/BrokeTheNews/index'),
    meta: { title: '我要爆料' },
  },
  {
    name: 'wxNewsHistory',
    path: '/wxNewsHistory',
    component: () => import('@/views/wxPages/newsHistory/index'),
    meta: { title: '我的上报记录' },
  },
  {
    name: 'wxNewsDetail',
    path: '/wxNewsDetail',
    component: () => import('@/views/wxPages/newsDetail/index'),
    meta: { title: '详情' },
  },
  {
    name: 'dogScan',
    path: '/dogScan',
    component: () => import('@/views/wxPages/dogScan/index'),
    meta: { title: '扫一扫' },
  },
]

const router = new VueRouter({
  // mode: 'history',
  //浙里办上架
  mode: 'hash',
  // base: '/ygfMobileJcZlb'
  //浙里办上架
  base: '/',
  routes,
})

// 在导出路由实例前添加守卫逻辑
router.beforeEach((to, from, next) => {
  // 排除登录页本身
  // if (to.path !== '/login') {
  //   // 检查是否存在token（假设token存储在localStorage）
  //   const token = getToken()
  //   if (!token) {
  //     next('/login') // 无token则跳转登录页
  //     return
  //   }
  // }
  next() // 放行其他情况
})

export default router
