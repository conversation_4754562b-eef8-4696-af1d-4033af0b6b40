import axios from 'axios'
import { Toast } from 'vant'
import { getToken } from '@/util/auth'
import errorCode from '@/util/errorCode'

axios.defaults.withCredentials = true
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API,
  // 超时
  // timeout: 100000,
  withCredentials: true
})
// request拦截器
service.interceptors.request.use(
  (config) => {
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false
    if (getToken() && !isToken) {
      config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      let url = config.url + '?'
      for (const propName of Object.keys(config.params)) {
        const value = config.params[propName]
        if (value === undefined || value === null) {
          continue; // 跳过空值
        }
        var part = encodeURIComponent(propName) + '='
        if (typeof value === 'object') {
          if (value === null) {
            continue; // 跳过null对象
          }
          for (const key of Object.keys(value)) {
            if (value[key] === undefined || value[key] === null) {
              continue; // 跳过对象中的空值
            }
            const params = propName + '[' + key + ']'
            var subPart = encodeURIComponent(params) + '='
            url += subPart + encodeURIComponent(value[key]) + '&'
          }
        } else {
          url += part + encodeURIComponent(value) + '&'
        }
      }
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }
    return config
  },
  (error) => {
    console.log(error)
    return Promise.reject(error)
  }
)
// 响应拦截器
service.interceptors.response.use(res => {
  // 未设置状态码则默认成功状态
  const code = res.data.code || 200
  // 获取错误信息
  console.log(res)
  const msg = res.data.msg || errorCode[code]   || errorCode['default']
  if (code === 500) {
    Toast.fail(msg)
    return Promise.reject(res.data)
  } else if (code !== 200) {
    Toast.fail(msg)
    return Promise.reject(res.data)
  } else {
    return res.data
  }
},
error => {
  console.log('err' + error)
  // alert('err' + JSON.stringify(error))
  let { message } = error
  if (message == 'Network Error') {
    message = '服务器连接异常'
  } else if (message.includes('timeout')) {
    message = '网络异常，请重试！'
  } else if (message.includes('Request failed with status code')) {
    message = '系统接口' + message.substr(message.length - 3) + '异常'
  }
  Toast.fail(message)
  return Promise.reject(error)
}
)

export default service
