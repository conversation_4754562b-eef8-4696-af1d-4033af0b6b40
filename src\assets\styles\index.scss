* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
img,.van-image {
  width: 100%;
}

// 引入适老化样式
@import './elder.scss';

/* 导航栏 */
.van-nav-bar__content {
  height: 50px;
  .van-nav-bar__left {
    .van-icon {
      width: 10px;
      height: 19px;
      color: #222;
    }
  }
  .van-nav-bar__title {
    font-size: 16px;
    color: #222;
  }
}

// 弹窗
.van-dialog {
  width: 270px;
  // height: 134px;
  background: #fff;
  border-radius: 5px;
  font-size: 16px;
  color: #444;
  .van-dialog__message {
    font-size: 14px;
    color: #9b9b9b;
    padding: 10px 23px;
  }
  .van-dialog__footer {
    height: 44px;
  }
  .van-dialog__confirm,
  .van-dialog__confirm:active {
    color: #327bf0;
  }
}

// 隐藏滚动条
// ::-webkit-scrollbar {
//   width: 0;
//   height: 0;
//   color: transparent;
// }

// 技术支持
.technical_support{
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 12px;
  color: #818086;
  background: #f6f6f6;
  width: 100%;
  height: 100px;
  text-align: center;
  padding: 0 30px;
  .support_tel{
    color: #318ace;
  }
}

.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
  overflow-x: hidden;
}

// 导航栏样式
.nav-right {
  display: flex;
  align-items: center;

  .save-draft {
    font-size: 14px;
    color: #1989fa;
    margin-right: 15px;
  }

  .search-icon {
    color: #1989fa;
  }
}

.van-tabs__line{
  background-color: #1989fa;
}
