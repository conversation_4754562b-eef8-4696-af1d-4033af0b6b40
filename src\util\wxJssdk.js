/**
 * 微信JSSDK配置工具
 */
import { getWxJssdkConfig } from '@/api/common'

// 全局状态管理
let wxJssdkState = {
  isConfiguring: false,
  isReady: false,
  configPromise: null,
  retryCount: 0,
  maxRetries: 3
}

/**
 * 初始化微信JSSDK配置
 * @param {Array} jsApiList - 需要使用的JS接口列表
 * @returns {Promise} 配置结果
 */
export function initWxJssdk(jsApiList = ['scanQRCode', 'getLocation', 'chooseImage', 'previewImage', 'uploadImage', 'downloadImage']) {
  // 如果正在配置中，返回现有的Promise
  if (wxJssdkState.isConfiguring && wxJssdkState.configPromise) {
    console.log('微信JSSDK正在配置中，等待配置完成...')
    return wxJssdkState.configPromise
  }

  // 如果已经配置成功，直接返回
  if (wxJssdkState.isReady && window.wx) {
    console.log('微信JSSDK已配置完成')
    return Promise.resolve({ status: 'already_ready' })
  }

  wxJssdkState.isConfiguring = true
  wxJssdkState.configPromise = new Promise((resolve, reject) => {
    console.log('开始初始化微信JSSDK...')

    // 检查是否在微信环境中
    if (!isWechatBrowser()) {
      const error = new Error('请在微信中打开')
      console.warn('环境检查失败:', error.message)
      wxJssdkState.isConfiguring = false
      wxJssdkState.configPromise = null
      reject(error)
      return
    }

    // 等待微信JSSDK加载完成
    const checkJWeixinLoaded = () => {
      if (window.jWeixin) {
        console.log('微信JSSDK已加载，开始配置...')
        performConfig()
      } else if (wxJssdkState.retryCount < wxJssdkState.maxRetries) {
        wxJssdkState.retryCount++
        console.log(`微信JSSDK未加载，第${wxJssdkState.retryCount}次重试...`)
        setTimeout(checkJWeixinLoaded, 1000)
      } else {
        const error = new Error('微信JSSDK加载超时，请检查网络连接或刷新页面重试')
        console.error('JSSDK加载失败:', error.message)
        wxJssdkState.isConfiguring = false
        wxJssdkState.configPromise = null
        wxJssdkState.retryCount = 0
        reject(error)
      }
    }

    const performConfig = () => {
      try {
        // 获取当前页面URL（去除hash部分，微信签名需要）
        const url = window.location.href.split('#')[0]
        console.log('配置URL:', url)

        // 调用后端接口获取配置信息
        getWxJssdkConfig({ url })
          .then(res => {
            console.log('获取到微信JSSDK配置:', res)

            // 验证配置数据完整性
            if (!res || !res.appId || !res.timestamp || !res.nonceStr || !res.signature) {
              throw new Error('微信JSSDK配置数据不完整')
            }

            const config = {
              debug: process.env.NODE_ENV === 'development', // 开发环境开启调试
              appId: res.appId, // 必填，公众号的唯一标识
              timestamp: res.timestamp, // 必填，生成签名的时间戳
              nonceStr: res.nonceStr, // 必填，生成签名的随机串
              signature: res.signature, // 必填，签名
              jsApiList: jsApiList // 必填，需要使用的JS接口列表
            }

            console.log('微信JSSDK配置参数:', config)

            // 配置微信JSSDK
            window.jWeixin.config(config)

            // 配置成功回调
            window.jWeixin.ready(() => {
              console.log('微信JSSDK配置成功')
              // 恢复window.wx供业务代码使用
              window.wx = window.jWeixin
              wxJssdkState.isReady = true
              wxJssdkState.isConfiguring = false
              wxJssdkState.retryCount = 0
              resolve(config)
            })

            // 配置失败回调
            window.jWeixin.error((err) => {
              console.error('微信JSSDK配置失败:', err)
              wxJssdkState.isConfiguring = false
              wxJssdkState.configPromise = null
              wxJssdkState.retryCount = 0

              // 根据错误类型提供更详细的错误信息
              let errorMessage = '微信JSSDK配置失败'
              if (err && err.errMsg) {
                switch (err.errMsg) {
                  case 'config:invalid signature':
                    errorMessage = '微信签名验证失败，请检查域名配置'
                    break
                  case 'config:invalid url domain':
                    errorMessage = '当前域名未在微信公众平台配置'
                    break
                  case 'config:invalid appid':
                    errorMessage = '微信AppID配置错误'
                    break
                  default:
                    errorMessage = `微信JSSDK配置失败: ${err.errMsg}`
                }
              }

              reject(new Error(errorMessage))
            })
          })
          .catch(error => {
            console.error('获取微信JSSDK配置失败:', error)
            wxJssdkState.isConfiguring = false
            wxJssdkState.configPromise = null
            wxJssdkState.retryCount = 0

            let errorMessage = '获取微信JSSDK配置失败'
            if (error.response) {
              errorMessage += `: HTTP ${error.response.status}`
            } else if (error.message) {
              errorMessage += `: ${error.message}`
            }

            reject(new Error(errorMessage))
          })
      } catch (error) {
        console.error('微信JSSDK配置过程出错:', error)
        wxJssdkState.isConfiguring = false
        wxJssdkState.configPromise = null
        wxJssdkState.retryCount = 0
        reject(error)
      }
    }

    // 开始检查JSSDK加载状态
    checkJWeixinLoaded()
  })

  return wxJssdkState.configPromise
}

/**
 * 检查是否在微信浏览器中
 * @returns {boolean}
 */
export function isWechatBrowser() {
  const ua = navigator.userAgent.toLowerCase()
  const isWechat = ua.includes('micromessenger')
  console.log('用户代理:', ua)
  console.log('是否在微信环境:', isWechat)
  return isWechat
}

/**
 * 重置微信JSSDK状态（用于重新初始化）
 */
export function resetWxJssdkState() {
  console.log('重置微信JSSDK状态')
  wxJssdkState.isConfiguring = false
  wxJssdkState.isReady = false
  wxJssdkState.configPromise = null
  wxJssdkState.retryCount = 0

  // 清除window.wx
  if (window.wx) {
    delete window.wx
  }
}

/**
 * 获取微信JSSDK当前状态
 * @returns {Object}
 */
export function getWxJssdkState() {
  return {
    ...wxJssdkState,
    hasJWeixin: !!window.jWeixin,
    hasWx: !!window.wx,
    userAgent: navigator.userAgent
  }
}

/**
 * 微信扫一扫
 * @param {Object} options - 扫描配置选项
 * @returns {Promise}
 */
export function wxScanQRCode(options = {}) {
  return new Promise((resolve, reject) => {
    if (!window.wx || !window.wx.scanQRCode) {
      reject(new Error('微信JSSDK未配置或不支持扫一扫功能'))
      return
    }

    const defaultOptions = {
      needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果
      scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
      success: (res) => {
        const result = res.resultStr || res.scanCodeMessage || ""
        resolve(result)
      },
      fail: (err) => {
        reject(new Error('扫描失败: ' + JSON.stringify(err)))
      }
    }

    const finalOptions = { ...defaultOptions, ...options }
    window.wx.scanQRCode(finalOptions)
  })
}

/**
 * 检查微信JSSDK是否已配置
 * @returns {boolean}
 */
export function isWxJssdkReady() {
  const isReady = !!(window.wx && window.wx.scanQRCode && wxJssdkState.isReady)
  console.log('微信JSSDK就绪状态:', {
    hasWx: !!window.wx,
    hasScanQRCode: !!(window.wx && window.wx.scanQRCode),
    stateReady: wxJssdkState.isReady,
    finalReady: isReady
  })
  return isReady
}

/**
 * 微信获取地理位置
 * @param {Object} options - 位置配置选项
 * @returns {Promise}
 */
export function wxGetLocation(options = {}) {
  return new Promise((resolve, reject) => {
    if (!window.wx || !window.wx.getLocation) {
      reject(new Error('微信JSSDK未配置或不支持获取位置功能'))
      return
    }

    const defaultOptions = {
      type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
      success: (res) => {
        resolve({
          latitude: res.latitude,
          longitude: res.longitude,
          speed: res.speed,
          accuracy: res.accuracy
        })
      },
      fail: (err) => {
        reject(new Error('获取位置失败: ' + JSON.stringify(err)))
      }
    }

    const finalOptions = { ...defaultOptions, ...options }
    window.wx.getLocation(finalOptions)
  })
}

/**
 * 微信选择图片
 * @param {Object} options - 选择图片配置选项
 * @returns {Promise}
 */
export function wxChooseImage(options = {}) {
  return new Promise((resolve, reject) => {
    if (!window.wx || !window.wx.chooseImage) {
      reject(new Error('微信JSSDK未配置或不支持选择图片功能'))
      return
    }

    const defaultOptions = {
      count: 1, // 默认9
      sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
      success: (res) => {
        resolve(res.localIds) // 返回选定照片的本地ID列表
      },
      fail: (err) => {
        reject(new Error('选择图片失败: ' + JSON.stringify(err)))
      }
    }

    const finalOptions = { ...defaultOptions, ...options }
    window.wx.chooseImage(finalOptions)
  })
}

/**
 * 微信预览图片
 * @param {Object} options - 预览图片配置选项
 * @returns {Promise}
 */
export function wxPreviewImage(options = {}) {
  return new Promise((resolve, reject) => {
    if (!window.wx || !window.wx.previewImage) {
      reject(new Error('微信JSSDK未配置或不支持预览图片功能'))
      return
    }

    if (!options.urls || !Array.isArray(options.urls) || options.urls.length === 0) {
      reject(new Error('预览图片需要提供urls数组'))
      return
    }

    const defaultOptions = {
      current: options.urls[0], // 当前显示图片的http链接
      urls: options.urls, // 需要预览的图片http链接列表
      success: () => {
        resolve()
      },
      fail: (err) => {
        reject(new Error('预览图片失败: ' + JSON.stringify(err)))
      }
    }

    const finalOptions = { ...defaultOptions, ...options }
    window.wx.previewImage(finalOptions)
  })
}
