import request from '@/util/request'
import Cookies from 'js-cookie'
import store from '@/store/index'
import {setToken} from "@/util/auth";
import invokeRequest from "@/util/invokeRequest";

// 登录方法
export function login(data) {
  return request({
    url: '/login',
    method: 'post',
    data: data,
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    method: 'get',
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get',
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post',
  })
}

// 获取用户区域
export function getAreaName() {
  return request({
    url: '/xzzfj/xzzfjZfsb/getArea',
    method: 'get',
  })
}

export function loginProd() {
  return new Promise((resolve, reject) => {
    let url = `/loginV2`
    request({
      url: url,
      method: 'get'
    }).then(res => {
      setToken(res.token)
      resolve(res)
    }).catch(() => reject())
  })
}

// 浙里办APP和支付宝登录方法
// export function loginOther(ticket) {
//   return new Promise((resolve, reject) => {
//     const userId = store.state.vuex_user_vol_id
//     let url = `/business/vol/user/zlbLogin/${ticket}`
//     let params = userId ? { userId } : {}
//     request({
//       url: url,
//       method: 'get',
//       params: params
//     }).then(res => {
//       const {authToken} = res.data
//
//       //暂时注释
//       // setToken(authToken)
//
//       // Cookies.set('ZlbAuthorization', authToken)
//       // const token = 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl96bGJfdXNlcl9rZXkiOiIxZDExYjEwYS01NjNkLTQzYjctOWE4NS1mMDRjYTZiMGI4MTIifQ.O91NIBEmB8a2lX-tbh9aK1P3BQRzDfH1HifCiFrkDgvwlNbvCsZNq2JvOFTh1SR3wLSU4oecMzaDM7Ji1W3Dug'
//       const token = res.data.authToken
//       store.commit('$uStore', { name: 'vuex_token', value: token })
//       resolve(res)
//     }).catch(() => reject())
//   })
// }

// 浙里办APP和支付宝登录方法
export function loginOther(ticket) {
  return new Promise((resolve, reject) => {
    // const userId = store.state.vuex_user_vol_id
    let url = `/ZLB/zlblogin`
    request({
      url: url,
      method: 'get',
      params: {ticketId: ticket}
    }).then(res => {
      // const {token} = res.data
      //
      // //暂时注释
      // setToken(token)
      //
      // // Cookies.set('ZlbAuthorization', authToken)
      // // const token = 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl96bGJfdXNlcl9rZXkiOiIxZDExYjEwYS01NjNkLTQzYjctOWE4NS1mMDRjYTZiMGI4MTIifQ.O91NIBEmB8a2lX-tbh9aK1P3BQRzDfH1HifCiFrkDgvwlNbvCsZNq2JvOFTh1SR3wLSU4oecMzaDM7Ji1W3Dug'
      // store.commit('$uStore', { name: 'vuex_token', value: token })
      resolve(res)
    }).catch(() => reject())
  })
}
