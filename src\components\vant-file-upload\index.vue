<template>
  <div class="vant-upload-file">
    <!-- 浙里办环境独立UI -->
    <div v-if="isZLBEnvironment" class="zlb-upload-area">
      <div class="zlb-upload-button" @click="handleZLBUpload" :disabled="uploading">
        <van-icon name="photograph" class="upload-icon" />
        <div class="upload-text">{{ uploading ? '上传中...' : uploadText }}</div>
      </div>
    </div>

    <!-- 非浙里办环境使用vant组件 -->
    <van-uploader
        v-else
        ref="upload"
        v-model="internalFileList"
        :max-count="limit"
        :accept="accept"
        :disabled="$attrs.disabled"
        :max-size="fileSize * 1024 * 1024"
        :before-read="handleBeforeUpload"
        :after-read="handleAfterRead"
        :upload-text="uploadText"
        :upload-icon="uploadIcon"
        capture="camera"
    >
      <template #default>
        <slot>
          <div class="upload-icon-wrapper">
            <van-icon name="photograph" class="upload-icon" />
          </div>
        </slot>
      </template>
    </van-uploader>

    <!-- 文件列表展示区域 -->
    <div class="file-list-container" v-if="showFileList && internalFileList.length > 0 && isZLBEnvironment">
      <div class="file-list-title">已上传文件</div>
      <!-- 文件列表 -->
      <template>
        <div
            v-for="(file, index) in internalFileList"
            :key="file.uid || index"
            class="file-item"
        >
          <div class="file-info" @click="handlePreview(file)">
            <van-icon name="description" class="file-icon" />
            <div class="file-name van-ellipsis">{{ file.name }}</div>
          </div>
          <div class="file-actions">
            <van-icon
                name="delete"
                class="delete-icon"
                @click="handleFileDelete(index)"
            />
          </div>
        </div>
      </template>
    </div>

    <!-- 提示信息 -->
    <div class="zlb-upload-tip" v-if="showTip">
      <p>
        <span>支持格式：{{ fileTypeText }}</span>
        <span v-if="fileSize">，大小不超过 {{ fileSize }}MB</span>
      </p>
    </div>
  </div>
</template>

<script>
import { getToken } from '@/util/auth'
import {getDownLoadUrl,isInZLBEnvironment} from "@/util";
import {ZlbUploadFile} from "@/api/common";
export default {
  name: 'index',

  props: {
    // 最大上传数量
    limit: {
      type: Number,
      default: 4
    },
    // 接受的文件类型
    accept: {
      type: String,
      default: 'image/*'
    },
    // 上传地址
    action: {
      type: String,
      default: process.env.VUE_APP_BASE_API + '/sysUploadFile/uploadFile'
    },
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: true
    },
    // 文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 额外参数
    data: {
      type: Object,
      default: () => ({})
    },
    // 上传的文件字段名
    name: {
      type: String,
      default: 'multipartFile'
    },
    // 值
    value: [String, Object, Array, Number],
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 20,
    },
    // 文件类型
    fileType: {
      type: Array,
      default: () => [
        'doc', 'docx', 'pdf', 'txt', 'xls', 'xlsx',
        'png', 'jpg', 'jpeg', 'gif',
        'mp3', 'mp4', 'mov', 'avi'
      ],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
    // 是否显示文件列表
    showFileList: {
      type: Boolean,
      default: true
    },
    // 上传图标
    uploadIcon: {
      type: String,
      default: 'photograph'
    },
    // 上传文本
    uploadText: {
      type: String,
      default: '上传文件'
    }
  },

  data() {
    return {
      headers: {
        Authorization: 'Bearer ' + getToken(),
      },
      internalFileList: [],
      isExpanded: false,
      uploading: false,
      isZLBEnvironment: isInZLBEnvironment()
    }
  },

  computed: {
    // 是否显示提示
    showTip() {
      if (this.$attrs.disabled) {
        return false
      }
      return this.isShowTip && (this.fileType || this.fileSize)
    },

    // 文件类型文本
    fileTypeText() {
      return this.fileType.join('、')
    }
  },

  watch: {
    fileList: {
      handler(val) {
        // 格式化文件列表，适配 Vant Uploader 的数据结构
        this.internalFileList = val.map(file => ({
          url: getDownLoadUrl(file),
          name: this.getFileName(file),
          isImage: /\.(jpeg|jpg|gif|png|webp)$/i.test(file || '')
        }))
      },
      immediate: true
    },

    value: {
      handler(val) {
        if (val) {
          this.fetchFiles(val)
          this.$emit('change', val);
        } else {
          // 如果值为空或undefined，也需要触发change事件
          this.internalFileList = []
          this.$emit('change', '');
        }
      },
      immediate: true
    },

    // 添加对内部文件列表的监听
    internalFileList: {
      handler(val) {
        // 当内部文件列表变化时，更新v-model值
        if (val && val.length > 0) {
          const fileString = val.map(item => item.uid || '').filter(Boolean).join(',')
          if (fileString && fileString !== this.value) {
            this.$emit('input', fileString)
            this.$emit('change', fileString) // 同时触发change事件
          }
        } else if (this.value) {
          // 如果文件列表为空但值不为空，则将值清空
          this.$emit('input', '')
          this.$emit('change', '') // 同时触发change事件
        }
      },
      deep: true
    },
  },

  created() {
    // 防止重复调用和初始值为空的情况
    if (this.value && !this.internalFileList.length) {
      this.fetchFiles(this.value)
    }
  },

  methods: {
    fetchFiles(val) {
      if (val) {
        const formattedFiles = val.split(',').filter(Boolean).map((item) => {
          return {
            name: this.getFileName(item),
            url: getDownLoadUrl(item),
            uid: item,
            isImage: /\.(jpeg|jpg|gif|png|webp)$/i.test(item || '')
          }
        })
        this.$emit('update:fileList', formattedFiles)
        this.internalFileList = formattedFiles
      }
    },

    // 上传前校验格式和大小
    handleBeforeUpload(file) {
      // 校验文件类型
      if (this.fileType && this.fileType.length > 0) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1).toLowerCase()
        }

        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && type.toLowerCase() === fileExtension) return true
          return false
        })

        if (!isTypeOk) {
          this.$toast.fail(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`)
          return false
        }
      }

      // 校验文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$toast.fail(`上传文件大小不能超过 ${this.fileSize}MB!`)
          return false
        }
      }

      return true
    },

    // 文件读取后的处理
    handleAfterRead(file) {
      console.log(file,"file");
      if (!this.autoUpload) {
        // 如果不自动上传，则只添加到文件列表
        this.$emit('select', file)
        return
      }

      // 自动上传逻辑
      this.uploadFile(file)
    },

    // 上传文件（仅用于非浙里办环境的vant组件）
    uploadFile(file) {
      this.uploading = true

      // 标记当前文件为上传中
      file.status = 'uploading'
      file.message = '上传中...'

      const formData = new FormData()

      // 添加文件
      if (file.file) {
        formData.append(this.name, file.file)
      }

      // 添加额外参数
      if (this.data) {
        Object.keys(this.data).forEach(key => {
          formData.append(key, this.data[key])
        })
      }

      console.log('使用默认fetch上传')
      this.uploadFileWithFetch(file, formData)
    },

    // 默认fetch上传
    uploadFileWithFetch(file,formData) {
      // 发起上传请求
      fetch(this.action, {
        method: 'POST',
        headers: this.headers,
        body: formData
      }).then(response => response.json()).then(response => {
            console.log('fetch上传响应:', response)
            this.handleUploadSuccess(file, response)
          })
          .catch(error => {
            console.error('fetch上传失败:', error)
            this.handleUploadError(file, error)
          })
    },

    // 处理上传成功
    handleUploadSuccess(file, response) {
      this.uploading = false

      // 统一处理不同环境的响应格式
      let responseData = response
      if (response.data) {
        responseData = response.data
      }

      if (response.code === 200 || response.success || responseData) {
        // 上传成功
        file.status = 'done'
        file.message = '上传成功'

        // 更新文件信息
        const fileId = responseData || response.fileId || response.data
        if (fileId) {
          Object.assign(file, {
            url: getDownLoadUrl(fileId),
            uid: fileId
          })

          // 更新v-model值
          const fileString = this.internalFileList.map(item => item.uid || '').filter(Boolean).join(',')
          this.$emit('input', fileString)
          this.$emit('change', fileString)
        }

        this.$emit('success', { response, file, fileList: this.internalFileList })
        this.$toast.success('文件上传成功')
      } else {
        // 上传失败
        this.handleUploadError(file, response.msg || response.message || '上传失败')
      }
    },

    // 处理上传失败
    handleUploadError(file, error) {
      this.uploading = false
      file.status = 'failed'
      file.message = typeof error === 'string' ? error : '上传失败'

      const errorMsg = error?.message || error || '文件上传失败，请重新上传'
      this.$toast.fail(errorMsg)
      this.$emit('error', errorMsg)
    },

    // 浙里办环境独立上传处理
    handleZLBUpload() {
      if (this.uploading) {
        this.$toast('正在上传中，请稍候...')
        return
      }

      // 检查是否达到上传限制
      if (this.internalFileList.length >= this.limit) {
        this.$toast(`最多只能上传${this.limit}个文件`)
        return
      }

      console.log('浙里办环境：开始选择文件')
      this.chooseFileInZLB()
    },

    // 浙里办环境选择文件
    chooseFileInZLB() {
      if (!window.ZWJSBridge) {
        this.$toast.fail('浙里办环境不可用')
        return
      }
      // 根据accept类型选择不同的方法
      if (this.accept && this.accept.includes('image')) {
        this.chooseImageInZLB()
      }
    },

    // 浙里办选择图片
    chooseImageInZLB() {
      if (!window.ZWJSBridge.chooseImage) {
        this.$toast.fail('选择图片功能不可用')
        return
      }

      const params = {
        upload: false,
        sourceType: ['album', 'camera']
      }

      window.ZWJSBridge.chooseImage(params).then((result) => {
        // result格式  { picPath: ["https://portal.zjzwfw.gov.cn/media/oss/image/PORTAL/22B7B989DF203F4072C9106DD81851F1.jpeg"],result: "true" }
        console.log('选择图片成功:', result)

        ZlbUploadFile({fileBase64: result.picSrc[0]})
            .then(response => {
              console.log('上传图片成功返回:', response)
              this.handleZLBFileSelected(response)
            })
      }).catch((error) => {
        console.error('选择图片失败:', error)
        this.$toast.fail('选择图片失败')
      })
    },

    // 处理浙里办文件选择结果
    handleZLBFileSelected(result) {
      // 检查浙里办返回格式
      if (!result.data) {
        this.$toast.fail('未选择任何文件')
        return
      }

      let path = result.data

      console.log('浙里办文件选择成功:', path)

      // 创建文件对象（直接标记为上传成功）
      const fileObj = {
        url: path,
        name: result.data.split(".")[0],
        file: { path: path },
        status: 'done',  // 直接标记为完成
        message: '上传成功',
        uid: path,
        isImage: /\.(jpeg|jpg|gif|png|webp)$/i.test(path)
      }

      console.log('添加文件到列表:', fileObj)
      console.log('添加文件到列表:', fileObj.url,fileObj.name,fileObj.file,fileObj.uid)

      // 添加到文件列表
      this.internalFileList.push(fileObj)

      // 更新v-model值
      this.updateFileListValue()

      this.$toast.success(`成功添加1张图片`)
    },

    // 获取文件扩展名
    getFileExtension(mimeType) {
      const extensions = {
        'image/jpeg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/webp': '.webp',
        'image/bmp': '.bmp'
      };

      return extensions[mimeType] || '.bin';
    },

    // 删除文件确认
    handleFileDelete(index) {
      this.$dialog.confirm({
        title: '提示',
        message: '是否确认删除？删除后将无法恢复',
      })
          .then(() => {
            const deletedFile = this.internalFileList[index]
            this.internalFileList.splice(index, 1)
            this.$emit('remove', deletedFile)
            this.$emit('update:fileList', [...this.internalFileList])
            this.$emit('input', this.internalFileList.map(item => item.uid).join(','))
          })
          .catch(() => {
            // 取消删除
          })
    },

    // 预览文件
    handlePreview(file) {
      this.$emit('preview', file)

      // 如果是图片，使用 Vant 图片预览组件预览
      if (file.isImage) {
        const images = this.internalFileList
            .filter(item => item.isImage)
            .map(item => 'https://ygf.xzzfj.jinhua.gov.cn/prod-api/sysUploadFile/downloadLocalFile?path=' + encodeURI(item.uid))

        console.log(images,"预览");
        console.log(this.internalFileList,"预览");
        const index = images.indexOf(file.url)

        this.$imagePreview({
          images,
          startPosition: index > -1 ? index : 0,
          closeable: true
        })
      }
      // 如果是其他类型文件，尝试在新窗口打开
      else if (file.url) {
        window.open(file.url)
      }
    },

    // 切换展开/收起状态
    toggleExpand() {
      this.isExpanded = !this.isExpanded
    },

    // 获取文件名称
    getFileName(url) {
      if (!url) return ''

      if (url.lastIndexOf('/') > -1) {
        return url.slice(url.lastIndexOf('/') + 1).toLowerCase()
      } else {
        return url
      }
    },

    // 从URL中提取文件名
    extractFileName(url) {
      try {
        const urlObj = new URL(url)
        const pathname = urlObj.pathname
        const fileName = pathname.split('/').pop()
        return fileName || '未知文件'
      } catch (error) {
        console.error('解析文件名失败:', error)
        return '未知文件'
      }
    },

    // 从URL中提取文件ID
    extractFileIdFromUrl(url) {
      try {
        const urlObj = new URL(url)
        const pathname = urlObj.pathname
        // 提取文件名作为ID（去掉扩展名）
        const fileName = pathname.split('/').pop()
        if (fileName) {
          const fileId = fileName.split('.')[0]
          return fileId
        }
        return null
      } catch (error) {
        console.error('解析文件ID失败:', error)
        return null
      }
    },

    // 更新文件列表的v-model值
    updateFileListValue() {
      console.log(this.internalFileList,"更新文件列表");
      const fileString = this.internalFileList
        .map(item => item.uid || '')
        .filter(Boolean)
        .join(',')

      console.log('更新v-model值:', fileString)
      this.$emit('input', fileString)
      this.$emit('change', fileString)
    },

    // 手动提交上传
    submit() {
      if (this.internalFileList.some(file => file.status === 'uploading')) {
        this.$toast('有文件正在上传中，请稍后再试')
        return
      }

      const pendingFiles = this.internalFileList.filter(file => !file.status || file.status === 'failed')

      if (pendingFiles.length === 0) {
        this.$toast('没有需要上传的文件')
        return
      }

      pendingFiles.forEach(file => {
        this.uploadFile(file)
      })
    },

    // 清空文件列表
    clearFiles() {
      this.internalFileList = []
      this.$emit('update:fileList', [])
      this.$emit('input', '')
    },
  }
}
</script>

<style scoped lang="scss">
.vant-upload-file {
  width: 100%;

  .upload-icon-wrapper {
    width: 83px;
    height: 83px;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: #F7F8FA;
  }

  .upload-icon {
    font-size: 24px;
    color: #969799;
  }

  .upload-text {
    margin-top: 8px;
    color: #969799;
    font-size: 12px;
  }

  .file-list-container {
    margin-top: 16px;

    .file-list-title {
      font-size: 14px;
      color: #323233;
      margin-bottom: 8px;
    }

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background-color: #f7f8fa;
      border-radius: 4px;
      margin-bottom: 8px;

      &.disabled {
        opacity: 0.7;
      }

      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;

        .file-icon {
          font-size: 20px;
          color: #646566;
          margin-right: 8px;
          flex-shrink: 0;
        }

        .file-name {
          font-size: 14px;
          color: #323233;
          flex: 1;
          min-width: 0;
        }
      }

      .file-actions {
        display: flex;
        align-items: center;

        .delete-icon {
          font-size: 18px;
          color: #ee0a24;
          padding: 4px;
        }
      }
    }

    .toggle-button {
      display: block;
      margin: 8px auto;
      font-size: 12px;
    }
  }

  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #969799;
    line-height: 1.5;
  }
}

/* 浙里办独立UI样式 */
.zlb-upload-area {
  width: 100%;
}

.zlb-upload-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 120px;
  background-color: #f7f8fa;
  border: 2px dashed #c8c9cc;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1989fa;
    background-color: #f0f9ff;
  }

  &:active {
    transform: scale(0.98);
  }

  &[disabled] {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      border-color: #c8c9cc;
      background-color: #f7f8fa;
    }
  }

  .upload-icon {
    font-size: 32px;
    color: #1989fa;
    margin-bottom: 8px;
  }

  .upload-text {
    font-size: 16px;
    color: #646566;
    font-weight: 500;
  }
}

.zlb-upload-tip {
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;

  p {
    margin: 0;
    font-size: 12px;
    color: #d46b08;
    line-height: 1.4;

    span {
      display: inline-block;
      margin-right: 8px;
    }
  }
}

/* 动画效果 */
.van-fade-enter-active,
.van-fade-leave-active {
  transition: opacity 0.3s ease;
}

.van-fade-enter,
.van-fade-leave-to {
  opacity: 0;
}
</style>
